stages:
  - build
  - sync_wiki
  - deploy_dev
  - backup_data

variables:
  CI_REGISTRY: ***************:5050
  CI_REGISTRY_IMAGE: ***************:5050/docker/ci-images/longer-thunderhub
  DEV_SERVER: ***************
  HTTP_PROXY: http://*************:10810
  NO_PROXY: *************/24,localhost,127.0.0.1,docker
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  DATA_STORAGE_DIR: /home/<USER>/thunderhub/data
  MONGO_IMAGE: $CI_REGISTRY/docker/ci-images/mongo:5.0
  REDIS_IMAGE: $CI_REGISTRY/docker/ci-images/redis:7.0
  CONSUL_IMAGE: $CI_REGISTRY/docker/ci-images/consul:1.20

# 通用 Docker 配置模板
.docker_setup:
  image: $CI_REGISTRY/docker/ci-images/docker:20.10
  services:
    - name: $CI_REGISTRY/docker/ci-images/docker:20.10-dind
      alias: docker
      command: ["--tls=false", "--insecure-registry=***************:5050"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:  
    - mkdir -p /etc/docker
    - |
      cat > /etc/docker/daemon.json <<EOF
      {
        "insecure-registries":["$CI_REGISTRY"],
        "proxies": {
          "http-proxy":"$HTTP_PROXY",
          "https-proxy":"$HTTP_PROXY",
          "no-proxy":"$NO_PROXY"
        }
      }
      EOF
    - export HTTP_PROXY="$HTTP_PROXY"
    - export HTTPS_PROXY="$HTTP_PROXY"
    - export NO_PROXY="$NO_PROXY"
    - for i in {1..30}; do docker info && break; sleep 2; done
    - docker info --format '{{.ServerVersion}}' || echo "Docker daemon not running"
    - echo "$CI_IMAGES_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY

build_wiki_image:
  extends: .docker_setup
  stage: build
  script:
    - docker build -t $CI_REGISTRY_IMAGE-ci-wiki-image:3.21 . || { echo "Docker build failed"; exit 1; }
    - docker push $CI_REGISTRY_IMAGE-ci-wiki-image:3.21 || { echo "Docker push failed"; exit 1; }
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      changes:
        - Dockerfile
  timeout: 1 hour

sync_wiki:
  stage: sync_wiki
  image: $CI_REGISTRY_IMAGE-ci-wiki-image:3.21
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    - git clone "http://oauth2:$WIKI_ACCESS_TOKEN@***************/${CI_PROJECT_PATH}.wiki.git" wiki || { echo "Git clone failed"; exit 1; }
    - cd wiki
    - git checkout main || git checkout -b main
    - rsync -av --delete --exclude '.git' ../docs/ .
    - git add .
    - if git diff --staged --quiet; then echo "No changes to commit"; else git commit -m "Sync docs to Wiki from commit $CI_COMMIT_SHA" && git push origin main; fi
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      changes:
        - docs/*
  timeout: 30 minutes
  
build_frontend_dependency_image:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build -f Dockerfile.dependencies -t $CI_REGISTRY_IMAGE-frontend-dependencies:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend-dependencies:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/package.json
        - frontend/package-lock.json
        - frontend/Dockerfile.dependencies
  timeout: 30 minutes

build_frontend_build_image:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -f Dockerfile.builder -t $CI_REGISTRY_IMAGE-frontend-build:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend-build:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/src/**/*
        - frontend/public/**/*
        - frontend/*.config.*  # 包含 vite.config.js, webpack.config.js 等
        - frontend/Dockerfile.builder
        - frontend/*.html
        - frontend/tsconfig.*
      when: always
  dependencies:
    - build_frontend_dependency_image
  timeout: 20 minutes

build_frontend_dev:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -t $CI_REGISTRY_IMAGE-frontend:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/nginx.conf
        - frontend/Dockerfile
      when: always
  dependencies:
    - build_frontend_build_image
  timeout: 10 minutes

build_base_image:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build -f Dockerfile.base -t $CI_REGISTRY_IMAGE-python-base:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-python-base:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main"'
      changes:
        - backend/Dockerfile.base
  timeout: 2 hours

build_dependency_image:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -f Dockerfile.dependencies -t $CI_REGISTRY_IMAGE-python-dependencies:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-python-dependencies:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main"'
      changes:
        - backend/Dockerfile.dependencies
        - backend/requirements.txt
  dependencies:
    - build_base_image
  timeout: 30 minutes

build_backend_dev:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -t $CI_REGISTRY_IMAGE-backend:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-backend:$IMAGE_TAG
  rules:
     - if: '$CI_COMMIT_BRANCH == "dev"'
       changes:
        - backend/src/**/*  # 假设代码文件在 backend/src/ 下
        - backend/scripts/**/*  # 假设其他代码在 backend/scripts/ 下
        - backend/Dockerfile  # 显式包含 Dockerfile
        # 其他特定子目录或文件模式
       when: always
  dependencies:
    - build_dependency_image
  timeout: 30 minutes

deploy_dev:
  extends: .docker_setup
  stage: deploy_dev
  before_script:
    - apk add --no-cache gettext  # 在基于 Alpine 的 Docker 镜像中安装 gettext（包含 envsubst）
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - head -n 2 ~/.ssh/id_rsa  # 打印密钥前两行以确认格式
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "docker --version && docker-compose --version" || { echo "Docker or Docker Compose not installed on $DEV_SERVER"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "mkdir -p $DATA_STORAGE_DIR/mongodb $DATA_STORAGE_DIR/redis $DATA_STORAGE_DIR/consul"
    - echo "$CI_IMAGES_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY || { echo "Docker login failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        docker image inspect $MONGO_IMAGE >/dev/null 2>&1 || docker pull $MONGO_IMAGE &&
        docker image inspect $REDIS_IMAGE >/dev/null 2>&1 || docker pull $REDIS_IMAGE &&
        docker image inspect $CONSUL_IMAGE >/dev/null 2>&1 || docker pull $CONSUL_IMAGE" || { echo "Failed to ensure images on $DEV_SERVER"; exit 1; }
  script:
    - envsubst < docker-compose.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/thunderhub/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/thunderhub &&
        docker-compose pull --ignore-pull-failures &&
        docker-compose up -d &&
        docker-compose ps --services" || { echo "Deployment failed"; exit 1; }
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/**/*
        - backend/**/*
        - docker-compose.yml  # 触发部署如果 docker-compose.yml 变更
      when: always
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: manual
      allow_failure: true
  dependencies:
    - build_frontend_dev
    - build_backend_dev
  timeout: 1 hour

backup_data:
  stage: backup_data
  image: $CI_REGISTRY/docker/ci-images/docker:20.10
  before_script:
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
  script:
    - mkdir -p data_backup/mongodb data_backup/redis data_backup/consul
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/mongodb/* data_backup/mongodb/ || echo "No MongoDB data to backup"
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/redis/* data_backup/redis/ || echo "No Redis data to backup"
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/consul/* data_backup/consul/ || echo "No Consul data to backup"
  artifacts:
    paths:
      - data_backup/
    expire_in: 1 month
    when: always
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: manual
      allow_failure: true
  timeout: 30 minutes