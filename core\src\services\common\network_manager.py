"""
网络管理器
负责网络连接检查和代理管理
"""

import logging
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class NetworkManager:
    """网络管理器类"""

    def __init__(self, device_id: str):
        """初始化网络管理器

        Args:
            device_id: 设备ID（可能是逻辑ID，需要通过设备管理器获取实际ID）
        """
        self.device_id = device_id
        self.device_manager = None  # 将在需要时设置

    def set_device_manager(self, device_manager):
        """设置设备管理器

        Args:
            device_manager: 设备管理器实例
        """
        self.device_manager = device_manager

    def _get_actual_device_id(self) -> str:
        """获取实际的设备ID

        Returns:
            str: 实际的设备ID
        """
        if self.device_manager:
            return self.device_manager.device_id
        return self.device_id

    async def check_network_connection(self) -> Dict[str, Any]:
        """检查网络连接

        Returns:
            Dict[str, Any]: 网络连接状态信息
        """
        try:
            logger.info(f"检查设备 {self.device_id} 的网络连接...")

            # 使用ADB检查Google连接
            google_accessible = await self._check_google_access()

            if google_accessible:
                logger.info("设备可以访问Google，网络连接正常")
                return {
                    "status": "success",
                    "google_accessible": True,
                    "message": "网络连接正常"
                }
            else:
                logger.warning("设备无法访问Google")

                # 检查基础网络连接（百度）
                basic_network = await self._check_basic_network()

                if basic_network:
                    logger.info("设备可以访问百度，基础网络连接正常")
                    return {
                        "status": "warning",
                        "google_accessible": False,
                        "basic_network": True,
                        "message": "网络连接受限，无法访问Google，但可以继续执行"
                    }
                else:
                    logger.error("设备无法访问互联网")
                    return {
                        "status": "error",
                        "google_accessible": False,
                        "basic_network": False,
                        "message": "网络连接异常，无法访问互联网"
                    }

        except Exception as e:
            logger.error(f"检查网络连接异常: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "message": f"检查网络连接异常: {str(e)}"
            }

    async def _check_google_access(self) -> bool:
        """检查Google访问

        Returns:
            bool: 是否可以访问Google
        """
        try:
            # 获取实际的设备ID
            actual_device_id = self._get_actual_device_id()
            logger.debug(f"检查Google访问，使用设备ID: {actual_device_id}")

            # 使用与原始版本相同的ping命令
            ping_process = await asyncio.create_subprocess_shell(
                f"adb -s {actual_device_id} shell ping -c 1 www.google.com",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            ping_stdout, ping_stderr = await ping_process.communicate()
            logger.debug(f"Google ping输出: {ping_stdout.decode()}")
            if ping_stderr:
                logger.debug(f"Google ping错误: {ping_stderr.decode()}")
            return ping_process.returncode == 0
        except Exception as e:
            logger.warning(f"检查Google访问异常: {str(e)}")
            return False

    async def _check_basic_network(self) -> bool:
        """检查基础网络连接（百度）

        Returns:
            bool: 是否可以访问基础网络
        """
        try:
            # 获取实际的设备ID
            actual_device_id = self._get_actual_device_id()
            logger.debug(f"检查基础网络，使用设备ID: {actual_device_id}")

            # 使用与原始版本相同的ping命令
            ping_process = await asyncio.create_subprocess_shell(
                f"adb -s {actual_device_id} shell ping -c 1 www.baidu.com",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            ping_stdout, ping_stderr = await ping_process.communicate()
            logger.debug(f"百度ping输出: {ping_stdout.decode()}")
            if ping_stderr:
                logger.debug(f"百度ping错误: {ping_stderr.decode()}")
            return ping_process.returncode == 0
        except Exception as e:
            logger.warning(f"检查基础网络异常: {str(e)}")
            return False

    async def ensure_google_access(self, v2ray_manager=None) -> bool:
        """确保Google访问

        Args:
            v2ray_manager: V2rayN管理器实例

        Returns:
            bool: 是否成功确保Google访问
        """
        try:
            # 首先检查当前Google访问状态
            if await self._check_google_access():
                logger.info("Google访问正常，无需启动代理")
                return True

            # 如果无法访问Google，尝试启动V2rayN
            if v2ray_manager:
                logger.info("无法访问Google，尝试启动V2rayN代理")
                v2ray_success = await v2ray_manager.launch_and_connect()

                if v2ray_success:
                    # 等待代理生效
                    await asyncio.sleep(3)

                    # 再次检查Google访问
                    if await self._check_google_access():
                        logger.info("启动V2rayN后，Google访问恢复正常")
                        return True
                    else:
                        logger.warning("启动V2rayN后仍无法访问Google")
                        return False
                else:
                    logger.warning("启动V2rayN失败")
                    return False
            else:
                logger.warning("没有提供V2rayN管理器，无法启动代理")
                return False

        except Exception as e:
            logger.error(f"确保Google访问异常: {str(e)}", exc_info=True)
            return False
