import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    chunkSizeWarningLimit: 2000,
    rollupOptions: {
      external: [
        'element-plus',
        'axios',
        'socket.io-client',
        'echarts',
        'pinia',
        'vue',
        'vue-router'
      ],
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('element-plus')) {
              return 'element-plus'
            }
            if (id.includes('echarts')) {
              return 'echarts'
            }
            if (id.includes('axios')) {
              return 'axios'
            }
            if (id.includes('socket.io-client')) {
              return 'socket-io'
            }
            if (id.includes('vue')) {
              return 'vue-vendor'
            }
            return 'vendor'
          }
          if (id.includes('PublishManagement')) {
            return 'publish-management'
          }
          if (id.includes('src/views/social')) {
            return 'social-module'
          }
          if (id.includes('src/views/device')) {
            return 'device-module'
          }
          if (id.includes('src/api')) {
            return 'api-module'
          }
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/element/index.scss" as *;`
      }
    }
  }
})
