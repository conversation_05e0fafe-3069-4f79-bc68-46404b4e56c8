"""
文件服务gRPC实现
"""

import os
import logging
import yaml
import time
from typing import Dict, List, Any, Optional

from src.main_service import CoreMainService
from src.api import file_pb2
from src.api import file_pb2_grpc

logger = logging.getLogger(__name__)

class FileServiceImpl(file_pb2_grpc.FileServiceServicer):
    """文件服务gRPC实现类"""

    def __init__(self, main_service: CoreMainService):
        """初始化文件服务

        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        logger.info("文件服务gRPC实现初始化")

    async def GetFilePaths(self, request, context):
        """获取文件路径配置"""
        try:
            platform_id = request.platform_id if request.platform_id else ""
            logger.info(f"收到获取文件路径配置请求: platform_id={platform_id}")
            logger.info(f"请求详情: {request}")
            logger.info(f"上下文详情: {context}")

            # 获取Core配置
            core_config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'core_config.yaml')
            core_config = {}

            try:
                if os.path.exists(core_config_path):
                    with open(core_config_path, 'r', encoding='utf-8') as f:
                        core_config = yaml.safe_load(f)
                    logger.info(f"成功加载Core配置: {core_config_path}")
                else:
                    logger.warning(f"Core配置文件不存在: {core_config_path}")
            except Exception as e:
                logger.error(f"加载Core配置失败: {str(e)}", exc_info=True)

            # 获取文件根路径
            file_root_path = "H:\\PublishSystem"  # 默认路径

            # 获取路径模板
            platform_path_template = "{root_path}\\{platform_id}"
            device_path_template = "{platform_path}\\{device_name}"
            content_path_template = "{device_path}\\content"

            # 如果Core配置中有文件路径配置，使用配置中的值
            if core_config and 'files' in core_config and 'paths' in core_config['files']:
                file_paths = core_config['files']['paths']
                file_root_path = file_paths.get('root_path', file_root_path)
                platform_path_template = file_paths.get('platform_path_template', platform_path_template)
                device_path_template = file_paths.get('device_path_template', device_path_template)
                content_path_template = file_paths.get('content_path_template', content_path_template)
                logger.info(f"从Core配置中获取文件路径配置: root_path={file_root_path}")
            else:
                logger.warning("Core配置中没有文件路径配置，使用默认值")

                # 如果没有文件路径配置，尝试从设备配置中获取
                if core_config and 'devices' in core_config and 'ldconsole_path' in core_config['devices']:
                    ldconsole_path = core_config['devices']['ldconsole_path']
                    if ldconsole_path:
                        # 使用ldconsole路径的父目录的父目录作为文件根路径
                        ldplayer_dir = os.path.dirname(ldconsole_path)
                        file_root_path = os.path.dirname(ldplayer_dir)
                        logger.info(f"从ldconsole路径获取文件根路径: {file_root_path}")

            # 构建响应
            response = file_pb2.FilePathsResponse()
            response.file_root_path = file_root_path
            response.platform_path_template = platform_path_template
            response.device_path_template = device_path_template
            response.content_path_template = content_path_template

            logger.info(f"返回文件路径配置: root_path={file_root_path}")
            logger.info(f"返回文件路径配置: platform_path_template={platform_path_template}")
            logger.info(f"返回文件路径配置: device_path_template={device_path_template}")
            logger.info(f"返回文件路径配置: content_path_template={content_path_template}")
            logger.info(f"完整响应对象: {response}")
            return response

        except Exception as e:
            logger.error(f"获取文件路径配置异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取文件路径配置失败: {str(e)}")
            return file_pb2.FilePathsResponse()

    async def ListDirectory(self, request, context):
        """列出目录内容"""
        try:
            path = request.path
            logger.info(f"收到列出目录请求: path={path}")

            # 检查路径是否存在
            if not os.path.exists(path):
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"路径不存在: {path}")
                return file_pb2.ListDirectoryResponse()

            # 检查是否是目录
            if not os.path.isdir(path):
                context.set_code(3)  # INVALID_ARGUMENT
                context.set_details(f"路径不是目录: {path}")
                return file_pb2.ListDirectoryResponse()

            # 列出目录内容
            response = file_pb2.ListDirectoryResponse()

            try:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    is_dir = os.path.isdir(item_path)

                    # 获取文件信息
                    file_info = response.files.add()
                    file_info.name = item
                    file_info.path = item_path
                    file_info.is_directory = is_dir

                    # 获取文件大小和修改时间
                    try:
                        stat_info = os.stat(item_path)
                        file_info.size = stat_info.st_size
                        file_info.modified_time = int(stat_info.st_mtime)
                    except Exception as e:
                        logger.warning(f"获取文件信息失败: {str(e)}")
                        file_info.size = 0
                        file_info.modified_time = 0

            except Exception as e:
                logger.error(f"列出目录内容失败: {str(e)}", exc_info=True)
                context.set_code(13)  # INTERNAL
                context.set_details(f"列出目录内容失败: {str(e)}")
                return file_pb2.ListDirectoryResponse()

            logger.info(f"返回目录内容，共{len(response.files)}个项目")
            return response

        except Exception as e:
            logger.error(f"列出目录异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"列出目录失败: {str(e)}")
            return file_pb2.ListDirectoryResponse()

    async def CheckPathExists(self, request, context):
        """检查路径是否存在"""
        try:
            path = request.path
            logger.info(f"收到检查路径请求: path={path}")

            # 检查路径是否存在
            exists = os.path.exists(path)
            is_directory = os.path.isdir(path) if exists else False

            # 构建响应
            response = file_pb2.PathExistsResponse()
            response.exists = exists
            response.is_directory = is_directory

            logger.info(f"路径 {path} 存在: {exists}, 是目录: {is_directory}")
            return response

        except Exception as e:
            logger.error(f"检查路径异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"检查路径失败: {str(e)}")
            return file_pb2.PathExistsResponse()
