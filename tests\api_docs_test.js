async function run(page) {
  try {
    await page.goto('http://localhost:5678/api/v1/docs/');
    await page.waitForSelector('.opblock-tag', { timeout: 5000 });

    return await page.evaluate(() => {
      return Array.from(document.querySelectorAll('.opblock-tag'))
        .flatMap(group => 
          Array.from(group.nextElementSibling.querySelectorAll('.opblock'))
            .map(api => ({
              method: api.querySelector('.opblock-summary-method').textContent.trim(),
              path: api.querySelector('.opblock-summary-path').textContent.trim()
            }))
        )
        .filter(api => api.path.includes('/workflows') || api.path.includes('/nodes'));
    });
  } catch (error) {
    return { error: error.message };
  }
}