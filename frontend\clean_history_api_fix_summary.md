# 清理历史任务API修复总结

## 🎯 问题描述

用户点击清理历史任务按钮时报错：
- `Failed to load resource: the server responded with a status of 404 (Not Found)`
- `清理历史记录失败: AxiosError`

## 🔍 问题分析

### 错误原因
1. **API接口缺失**：后端没有 `/api/tasks/history/clean` 接口
2. **路由未注册**：清理历史任务的API没有正确添加到路由中
3. **前后端不匹配**：前端调用的接口路径与后端实现不一致

### 前端调用
```javascript
// frontend/src/api/task.ts
export const cleanTaskHistory = (params: {
  before_date?: string
  status?: string
  keep_count?: number
}) => {
  return request({
    url: '/api/tasks/history/clean',  // 调用这个路径
    method: 'post',
    data: params
  })
}
```

### 后端缺失
- 后端 `task.py` 文件中没有对应的 `/history/clean` 接口
- 导致前端请求返回404错误

## 🛠️ 修复方案

### 1. 添加清理历史任务API

**新增接口**: `POST /api/tasks/history/clean`

```python
@router.post("/history/clean")
async def clean_task_history(request: Request):
    """清理任务历史"""
    try:
        # 获取请求数据
        clean_data = await request.json()
        logger.info(f"清理任务历史: {clean_data}")
        
        # 获取数据库连接
        db = request.app.state.mongo_db
        db_service = SocialDatabaseService(db)
        
        # 构建删除条件
        delete_query = {}
        
        # 支持三种清理方式...
```

### 2. 支持的清理方式

#### 方式1：按日期清理
```python
# 按日期清理
if "before_date" in clean_data and clean_data["before_date"]:
    before_date = clean_data["before_date"]
    date_obj = dt.datetime.strptime(before_date, "%Y-%m-%d")
    delete_query["created_at"] = {"$lt": date_obj}
    logger.info(f"按日期清理: 删除 {before_date} 之前的任务")
```

#### 方式2：按状态清理
```python
# 按状态清理
elif "status" in clean_data and clean_data["status"]:
    status = clean_data["status"]
    delete_query["status"] = status
    logger.info(f"按状态清理: 删除状态为 {status} 的任务")
```

#### 方式3：按保留数量清理
```python
# 按保留数量清理
elif "keep_count" in clean_data and clean_data["keep_count"]:
    keep_count = int(clean_data["keep_count"])
    logger.info(f"按保留数量清理: 保留最新 {keep_count} 个任务")
    
    # 查询所有任务，按创建时间倒序排列
    all_tasks = list(db_service.db.social_tasks.find({}).sort("created_at", -1))
    
    if len(all_tasks) > keep_count:
        # 获取需要删除的任务ID
        tasks_to_delete = all_tasks[keep_count:]
        task_ids_to_delete = [task["task_id"] for task in tasks_to_delete if "task_id" in task]
        delete_query = {"task_id": {"$in": task_ids_to_delete}}
```

### 3. 执行删除操作

```python
# 执行删除操作
if delete_query:
    logger.info(f"执行删除操作，查询条件: {delete_query}")
    result = db_service.db.social_tasks.delete_many(delete_query)
    deleted_count = result.deleted_count
    
    logger.info(f"清理完成，删除了 {deleted_count} 个任务")
    return {
        "success": True,
        "deleted_count": deleted_count,
        "message": f"成功清理了 {deleted_count} 个历史任务"
    }
```

### 4. 错误处理

```python
# 日期格式错误处理
try:
    date_obj = dt.datetime.strptime(before_date, "%Y-%m-%d")
except ValueError as e:
    logger.error(f"日期格式错误: {before_date}, {e}")
    return {"success": False, "error": "日期格式错误"}

# 通用错误处理
except Exception as e:
    logger.error(f"清理任务历史失败: {str(e)}", exc_info=True)
    return {
        "success": False,
        "error": f"清理任务历史失败: {str(e)}"
    }
```

## 📊 API接口规范

### 请求格式
```http
POST /api/tasks/history/clean
Content-Type: application/json

{
  "before_date": "2024-01-01",     // 可选：删除此日期之前的任务
  "status": "completed",           // 可选：删除指定状态的任务
  "keep_count": 100               // 可选：保留最新N个任务
}
```

### 响应格式
```json
{
  "success": true,
  "deleted_count": 25,
  "message": "成功清理了 25 个历史任务"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "请指定清理条件"
}
```

## 🎯 清理策略

### 1. 按日期清理
- **用途**：清理指定日期之前的所有任务
- **参数**：`before_date: "2024-01-01"`
- **查询条件**：`{"created_at": {"$lt": date_obj}}`

### 2. 按状态清理
- **用途**：清理指定状态的任务（如已完成、失败等）
- **参数**：`status: "completed"`
- **查询条件**：`{"status": "completed"}`

### 3. 按保留数量清理
- **用途**：保留最新的N个任务，删除其余的
- **参数**：`keep_count: 100`
- **逻辑**：按创建时间倒序排列，保留前N个，删除其余

## 🔧 技术实现细节

### 1. 数据库操作
```python
# 批量删除
result = db_service.db.social_tasks.delete_many(delete_query)
deleted_count = result.deleted_count
```

### 2. 日期处理
```python
import datetime as dt
date_obj = dt.datetime.strptime(before_date, "%Y-%m-%d")
```

### 3. 排序和分页
```python
# 按创建时间倒序排列
all_tasks = list(db_service.db.social_tasks.find({}).sort("created_at", -1))
```

### 4. 任务ID提取
```python
task_ids_to_delete = [task["task_id"] for task in tasks_to_delete if "task_id" in task]
```

## 🚀 修复效果

### 修复前的问题
- ❌ **404错误**：接口不存在，无法清理历史任务
- ❌ **功能缺失**：清理按钮无法正常工作
- ❌ **用户体验差**：无法管理历史任务数据

### 修复后的改进
- ✅ **接口完整**：提供完整的清理历史任务API
- ✅ **多种清理方式**：支持按日期、状态、数量清理
- ✅ **安全可靠**：完整的错误处理和日志记录
- ✅ **用户友好**：清晰的成功/失败反馈

## 🎯 使用示例

### 前端调用示例
```javascript
// 按日期清理
await cleanTaskHistory({ before_date: "2024-01-01" })

// 按状态清理
await cleanTaskHistory({ status: "completed" })

// 按数量清理
await cleanTaskHistory({ keep_count: 100 })
```

### 后端日志示例
```
INFO: 清理任务历史: {'keep_count': 100}
INFO: 按保留数量清理: 保留最新 100 个任务
INFO: 将删除 25 个任务
INFO: 执行删除操作，查询条件: {'task_id': {'$in': [...]}}
INFO: 清理完成，删除了 25 个任务
```

## 🎉 总结

通过添加 `/api/tasks/history/clean` 接口：

1. **解决了404错误**：后端现在有对应的API接口
2. **支持多种清理方式**：灵活的清理策略满足不同需求
3. **完善了错误处理**：提供详细的错误信息和日志
4. **提升了用户体验**：清理功能现在可以正常工作

清理历史任务功能现在完全可用！🚀
