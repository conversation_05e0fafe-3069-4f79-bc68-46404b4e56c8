"""
Core服务主入口
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
import grpc
import platform
import time
from concurrent import futures
from typing import Dict, List, Any, Optional

from src.config.settings import CoreSettings
from src.main_service import CoreMainService
from src.services.consul import ConsulRegistrationService
from src.services.device_sync import DeviceSyncService
from src.api.device_service import DeviceServiceImpl
from src.api import device_pb2_grpc
from src.api.file_service import FileServiceImpl
from src.api import file_pb2_grpc
from src.api.task_service import TaskServiceImpl
from src.api import task_pb2_grpc

# 日志配置已在 src/__init__.py 中完成，这里只设置级别
logging.getLogger().setLevel(logging.INFO)

logger = logging.getLogger(__name__)

class CoreServer:
    """Core服务器类"""

    def __init__(self, config_path: Optional[str] = None):
        """初始化Core服务器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.settings = CoreSettings(config_path)
        self.main_service = CoreMainService(config_path)
        self.grpc_server = None
        self.consul_service = None
        self.device_sync_service = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        logger.info(f"Core服务初始化，ID: {self.settings.core_id}")

    async def start(self) -> None:
        """启动服务"""
        try:
            # 初始化主服务
            init_success = await self.main_service.initialize()
            if not init_success:
                logger.error("初始化主服务失败")
                return

            # 启动gRPC服务
            await self._start_grpc_server()

            # 注册到Consul
            await self._register_to_consul()

            # 启动设备同步服务
            await self._start_device_sync_service()

            # 标记为运行中
            self.is_running = True

            # 等待关闭信号
            await self.shutdown_event.wait()

            # 关闭服务
            await self.shutdown()

        except Exception as e:
            logger.error(f"启动Core服务异常: {str(e)}", exc_info=True)

    async def _start_grpc_server(self) -> None:
        """启动gRPC服务器"""
        # 创建gRPC服务器
        self.grpc_server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=10))

        # 注册设备服务
        device_service = DeviceServiceImpl(self.main_service)
        device_pb2_grpc.add_DeviceServiceServicer_to_server(device_service, self.grpc_server)

        # 注册文件服务
        file_service = FileServiceImpl(self.main_service)
        file_pb2_grpc.add_FileServiceServicer_to_server(file_service, self.grpc_server)

        # 注册任务服务
        task_service = TaskServiceImpl(self.main_service)
        task_pb2_grpc.add_TaskServiceServicer_to_server(task_service, self.grpc_server)

        # 添加端口
        port = self.settings.grpc_port
        self.grpc_server.add_insecure_port(f'[::]:{port}')

        # 启动服务器
        await self.grpc_server.start()

        logger.info(f"gRPC服务已启动，监听端口: {port}")

    async def _register_to_consul(self) -> None:
        """注册服务到Consul"""
        # 创建Consul服务
        logger.info(f"准备注册服务到Consul: {self.settings.consul_host}:{self.settings.consul_port}")

        self.consul_service = ConsulRegistrationService(
            consul_host=self.settings.consul_host,
            consul_port=self.settings.consul_port,
            service_id=self.settings.core_id,
            service_name=self.settings.service_name,
            service_port=self.settings.grpc_port,
            tags=["core", "device", "windows", f"name={self.settings.core_name}"]  # 添加name标签
        )

        # 注册服务
        success = await self.consul_service.register()
        if success:
            logger.info(f"服务已注册到Consul: {self.settings.consul_host}:{self.settings.consul_port}")
        else:
            logger.warning(f"注册到Consul失败: {self.settings.consul_host}:{self.settings.consul_port}")

        # 检查注册状态
        await asyncio.sleep(2)  # 等待注册生效
        health_status = await self.consul_service.check_service_health()
        if health_status:
            logger.info("服务健康检查通过")
        else:
            logger.warning("服务健康检查未通过，请检查服务状态")

    async def _start_device_sync_service(self) -> None:
        """启动设备同步服务"""
        try:
            # 从配置获取同步间隔，默认3600秒（1小时）
            sync_interval = self.settings.device_sync_interval if hasattr(self.settings, 'device_sync_interval') else 3600

            # 从配置获取Redis URL
            redis_url = self.settings.redis_url if hasattr(self.settings, 'redis_url') else None

            # 从配置获取后端URL（备用方案）
            backend_url = self.settings.backend_url if hasattr(self.settings, 'backend_url') else None

            # 创建设备同步服务
            self.device_sync_service = DeviceSyncService(
                ldplayer_manager=self.main_service.ldplayer_manager,
                redis_url=redis_url,
                backend_url=backend_url,
                sync_interval=sync_interval,
                core_id=self.settings.core_id  # 传递Core服务ID
            )

            # 启动同步服务
            await self.device_sync_service.start()
            logger.info(f"设备同步服务已启动，同步间隔: {sync_interval}秒")

        except Exception as e:
            logger.error(f"启动设备同步服务异常: {str(e)}", exc_info=True)

    async def shutdown(self) -> None:
        """关闭服务"""
        if not self.is_running:
            return

        logger.info("正在关闭Core服务...")

        # 从Consul注销
        if self.consul_service:
            await self.consul_service.deregister()
            logger.info("已从Consul注销")

        # 关闭设备同步服务
        if self.device_sync_service:
            await self.device_sync_service.stop()
            logger.info("设备同步服务已关闭")

        # 关闭gRPC服务器
        if self.grpc_server:
            await self.grpc_server.stop(5)  # 5秒优雅关闭
            logger.info("gRPC服务已关闭")

        # 关闭主服务
        await self.main_service.shutdown()

        # 标记为已关闭
        self.is_running = False

        logger.info("Core服务已完全关闭")

    def signal_handler(self) -> None:
        """信号处理函数"""
        logger.info("收到关闭信号")
        self.shutdown_event.set()

# 全局服务器实例，用于热重载
_server_instance = None

def get_main_service():
    """获取主服务实例，用于热重载"""
    global _server_instance
    if _server_instance:
        return _server_instance.main_service
    return None

async def main() -> None:
    """主函数"""
    global _server_instance

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='ThunderHub Core Service')
    parser.add_argument('--config', type=str, help='配置文件路径')
    args = parser.parse_args()

    # 获取配置文件路径
    config_path = args.config
    if not config_path:
        # 默认配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'core_config.yaml')
        if os.path.exists(config_path):
            logger.info(f"使用默认配置文件: {config_path}")
        else:
            logger.warning(f"默认配置文件不存在: {config_path}")
            config_path = None

    # 创建服务器
    _server_instance = CoreServer(config_path)
    logger.info(f"创建服务器实例: {_server_instance}")

    # 注册信号处理 - 仅在非Windows平台上使用add_signal_handler
    if platform.system() != 'Windows':
        for sig in (signal.SIGINT, signal.SIGTERM):
            asyncio.get_event_loop().add_signal_handler(sig, _server_instance.signal_handler)
    else:
        # Windows平台使用信号处理替代方案
        logger.info("在Windows平台上运行，使用替代信号处理方案")

        # 设置信号处理函数
        signal.signal(signal.SIGINT, lambda sig, frame: _server_instance.signal_handler())
        signal.signal(signal.SIGTERM, lambda sig, frame: _server_instance.signal_handler())

    # 启动服务
    await _server_instance.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到键盘中断，正在退出...")







