http:
  routers:
    api-auth-router:
      rule: "PathPrefix(`/api`) || PathPrefix(`/auth`)"
      service: backend-service
      entryPoints:
        - web
      middlewares:
        - cors-middleware
    grpc-router:
      rule: "PathPrefix(`/grpc`)"
      service: backend-service
      entryPoints:
        - web
      middlewares:
        - grpc-middleware
        - cors-middleware
    socketio-auth-router:
      rule: "PathPrefix(`/auth-socket.io`)"
      service: backend-service
      entryPoints:
        - web
      middlewares:
        - cors-middleware
    socketio-devices-router:
      rule: "PathPrefix(`/devices-socket.io`)"
      service: backend-service
      entryPoints:
        - web
      middlewares:
        - cors-middleware
  services:
    backend-service:
      loadBalancer:
        servers:
          - url: "http://backend:8000"
  middlewares:
    grpc-middleware:
      headers:
        customRequestHeaders:
          Content-Type: "application/grpc"
    cors-middleware:
      headers:
        accessControlAllowOriginList:
          - "http://***************"
          - "http://localhost:5173"
          - "http://127.0.0.1:5173"
          - "http://*************:5173"
        accessControlAllowMethods:
          - "GET"
          - "POST"
          - "OPTIONS"
        accessControlAllowHeaders:
          - "Content-Type"
          - "Authorization"
          - "Accept"
          - "Origin"
        accessControlAllowCredentials: true
        accessControlMaxAge: 86400