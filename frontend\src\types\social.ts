// 社媒平台类型
export interface SocialPlatform {
  id: string
  name: string
  icon?: string
  website?: string
  status?: 'active' | 'inactive' | 'maintenance'
  features?: string[]
  description?: string
  color?: string
  category?: string
  auth_type?: string
  created_at?: string | number
  updated_at?: string | number
  app_info?: {
    android_package?: string
    ios_bundle_id?: string
    appium_support?: boolean
    adb_support?: boolean
    [key: string]: any
  }
  [key: string]: any
}

// 平台应用类型
export interface PlatformApp {
  id: string
  platform_id: string
  name: string
  type: 'android' | 'ios' | 'web' | 'desktop'
  status?: 'active' | 'inactive' | 'deprecated'
  version?: string
  package_name?: string // Android包名
  main_activity?: string // 主Activity
}

// 社媒账号类型
export interface SocialAccount {
  id: string
  username: string
  password?: string
  recovery_email?: string
  recovery_code?: string
  display_name?: string
  platform_id: string
  core_service_id: string
  status?: 'active' | 'inactive' | 'suspended'
  avatar?: string
  description?: string
  followers?: number
  following?: number
  posts_count?: number
  last_login?: string
  created_at?: string
  updated_at?: string
  tags?: string[]
  device_id?: string
  device_name?: string
  linked_device_id?: string | null  // 关联的设备ID
  appId?: string // 兼容旧版
  // 添加任意属性，以便动态添加属性
  [key: string]: any
}

// 设备账号映射
export interface DeviceAccountMapping {
  id: string
  device_id: string
  account_id: string
  platform_id: string
  app_id: string
  status?: 'active' | 'inactive'
  created_at?: string
  updated_at?: string
  last_used?: string
  core_service_id?: string
}

// 发布内容类型
export interface SocialPost {
  id: string
  accountId: string
  content: string
  mediaUrls?: string[]
  scheduledTime?: string
  status: 'draft' | 'scheduled' | 'published' | 'failed'
  publishResult?: {
    url?: string
    views?: number
    likes?: number
    comments?: number
  }
}

// 分析数据类型
export interface SocialAnalytics {
  date: string
  followers: number
  views: number
  likes: number
  comments: number
  shares: number
}