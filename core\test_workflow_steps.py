#!/usr/bin/env python3
"""
测试YouTube Shorts工作流步骤
"""

import yaml
import os

def test_workflow_steps():
    """测试工作流步骤顺序"""
    print("🔧 测试YouTube Shorts工作流步骤...")
    
    # 加载工作流配置
    workflow_path = os.path.join(
        os.path.dirname(__file__), 
        'config', 'platforms', 'youtube', 'workflows', 'shorts_upload.yaml'
    )
    
    if not os.path.exists(workflow_path):
        print(f"❌ 工作流文件不存在: {workflow_path}")
        return
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_config = yaml.safe_load(f)
    
    steps = workflow_config['workflow']['steps']
    
    print(f"📋 工作流包含 {len(steps)} 个步骤:")
    print()
    
    # 查找关键步骤
    music_step = None
    audio_step = None
    editor_step = None
    title_step = None
    
    for i, step in enumerate(steps, 1):
        step_name = step.get('name', 'Unknown')
        step_id = step.get('id', 'unknown')
        required = step.get('required', False)
        
        print(f"{i:2d}. {step_name}")
        print(f"    ID: {step_id}")
        print(f"    必需: {'是' if required else '否'}")
        
        # 记录关键步骤
        if step_id == 'music_handling':
            music_step = i
        elif step_id == 'audio_settings':
            audio_step = i
        elif step_id == 'go_to_editor':
            editor_step = i
        elif step_id == 'input_title':
            title_step = i
        
        print()
    
    # 验证步骤顺序
    print("🔍 验证关键步骤顺序:")
    
    if music_step and audio_step and editor_step and title_step:
        print(f"✅ 音乐处理步骤: {music_step}")
        print(f"✅ 音频设置步骤: {audio_step}")
        print(f"✅ 前往编辑器步骤: {editor_step}")
        print(f"✅ 输入标题步骤: {title_step}")
        
        # 检查顺序是否正确
        if music_step < audio_step < editor_step < title_step:
            print("✅ 步骤顺序正确: 音乐处理 → 音频设置 → 前往编辑器 → 输入标题")
        else:
            print("❌ 步骤顺序不正确")
    else:
        print("❌ 缺少关键步骤")
        if not music_step:
            print("   - 缺少音乐处理步骤")
        if not audio_step:
            print("   - 缺少音频设置步骤")
        if not editor_step:
            print("   - 缺少前往编辑器步骤")
        if not title_step:
            print("   - 缺少输入标题步骤")
    
    # 检查前往编辑器步骤的配置
    if editor_step:
        editor_config = steps[editor_step - 1]
        print(f"\n📝 前往编辑器步骤详情:")
        print(f"   名称: {editor_config.get('name')}")
        print(f"   动作: {editor_config.get('action')}")
        print(f"   元素: {editor_config.get('element')}")
        print(f"   必需: {editor_config.get('required')}")
        print(f"   等待时间: {editor_config.get('wait_after')}秒")
        print(f"   说明: {editor_config.get('notes')}")
        
        if editor_config.get('required') and editor_config.get('action') == 'click':
            print("✅ 前往编辑器步骤配置正确")
        else:
            print("❌ 前往编辑器步骤配置有问题")
    
    print("\n🎉 工作流步骤测试完成")

if __name__ == "__main__":
    test_workflow_steps()
