"""
音乐库管理API
"""
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import logging

from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)
router = APIRouter()

def get_music_service(request: Request):
    """获取音乐库服务实例"""
    db = request.app.state.mongo_db
    service = SocialDatabaseService(db)
    return service

class MusicItem(BaseModel):
    """音乐项目模型"""
    music_id: str
    title: str
    tags: List[str] = []
    duration: Optional[str] = None
    category: Optional[str] = "背景音乐"
    search_keywords: Optional[str] = None
    platform: str = "youtube"
    is_active: bool = True

@router.get("/")
async def get_music_library(
    platform: str = Query("youtube", description="平台名称"),
    category: Optional[str] = Query(None, description="音乐分类"),
    tags: Optional[str] = Query(None, description="标签（逗号分隔）"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    db_service: SocialDatabaseService = Depends(get_music_service)
):
    """获取音乐库列表"""
    try:
        collection = db_service.db.music_library

        # 构建查询条件
        query = {"platform": platform, "is_active": True}

        if category:
            query["category"] = category

        if tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            query["tags"] = {"$in": tag_list}

        if search:
            query["$or"] = [
                {"title": {"$regex": search, "$options": "i"}},
                {"music_id": {"$regex": search, "$options": "i"}},
                {"search_keywords": {"$regex": search, "$options": "i"}}
            ]

        # 查询数据
        cursor = collection.find(query).skip(skip).limit(limit)
        music_items = []

        for item in cursor:
            music_item = {
                "id": str(item["_id"]),
                "music_id": item["music_id"],
                "title": item["title"],
                "tags": item.get("tags", []),
                "duration": item.get("duration"),
                "category": item.get("category", "背景音乐"),
                "search_keywords": item.get("search_keywords"),
                "platform": item["platform"],
                "is_active": item.get("is_active", True)
            }
            music_items.append(music_item)

        # 获取总数
        total = collection.count_documents(query)

        return {
            "code": 200,
            "data": {
                "items": music_items,
                "total": total,
                "skip": skip,
                "limit": limit
            },
            "message": "获取音乐库成功"
        }

    except Exception as e:
        logger.error(f"获取音乐库失败: {str(e)}")
        return {
            "code": 500,
            "message": f"获取音乐库失败: {str(e)}"
        }

@router.post("/")
async def create_music_item(
    music_item: MusicItem,
    db_service: SocialDatabaseService = Depends(get_music_service)
):
    """创建音乐项目"""
    try:
        collection = db_service.db.music_library

        # 检查music_id是否已存在
        existing = collection.find_one({
            "music_id": music_item.music_id,
            "platform": music_item.platform
        })

        if existing:
            return {
                "code": 400,
                "message": f"音乐ID {music_item.music_id} 在平台 {music_item.platform} 中已存在"
            }

        # 创建音乐项目
        music_data = music_item.model_dump()
        music_data["created_at"] = datetime.now()
        music_data["updated_at"] = datetime.now()

        # 自动生成搜索关键词
        if not music_data.get("search_keywords"):
            keywords = [music_item.title, music_item.music_id] + music_item.tags
            music_data["search_keywords"] = " ".join(keywords)

        result = collection.insert_one(music_data)

        return {
            "code": 200,
            "data": {"id": str(result.inserted_id)},
            "message": "创建音乐项目成功"
        }

    except Exception as e:
        logger.error(f"创建音乐项目失败: {str(e)}")
        return {
            "code": 500,
            "message": f"创建音乐项目失败: {str(e)}"
        }

@router.get("/categories")
async def get_music_categories(
    platform: str = Query("youtube", description="平台名称"),
    db_service: SocialDatabaseService = Depends(get_music_service)
):
    """获取音乐分类列表"""
    try:
        collection = db_service.db.music_library

        # 获取所有分类
        categories = collection.distinct("category", {"platform": platform, "is_active": True})

        return {
            "code": 200,
            "data": {"categories": categories},
            "message": "获取音乐分类成功"
        }

    except Exception as e:
        logger.error(f"获取音乐分类失败: {str(e)}")
        return {
            "code": 500,
            "message": f"获取音乐分类失败: {str(e)}"
        }

@router.get("/tags")
async def get_music_tags(
    platform: str = Query("youtube", description="平台名称"),
    db_service: SocialDatabaseService = Depends(get_music_service)
):
    """获取音乐标签列表"""
    try:
        collection = db_service.db.music_library

        # 获取所有标签
        pipeline = [
            {"$match": {"platform": platform, "is_active": True}},
            {"$unwind": "$tags"},
            {"$group": {"_id": "$tags"}},
            {"$sort": {"_id": 1}}
        ]

        tags = [item["_id"] for item in collection.aggregate(pipeline)]

        return {
            "code": 200,
            "data": {"tags": tags},
            "message": "获取音乐标签成功"
        }

    except Exception as e:
        logger.error(f"获取音乐标签失败: {str(e)}")
        return {
            "code": 500,
            "message": f"获取音乐标签失败: {str(e)}"
        }
