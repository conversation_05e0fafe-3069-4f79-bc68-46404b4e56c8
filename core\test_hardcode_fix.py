#!/usr/bin/env python3
"""
测试硬编码修复效果
"""

import yaml
import os
import re

def test_hardcode_fix():
    """测试硬编码修复效果"""
    print("🔧 测试硬编码修复效果...")
    
    # 1. 检查工作流配置
    print("\n📋 检查工作流配置...")
    workflow_path = os.path.join(
        os.path.dirname(__file__), 
        'config', 'platforms', 'youtube', 'workflows', 'shorts_upload.yaml'
    )
    
    if not os.path.exists(workflow_path):
        print(f"❌ 工作流文件不存在: {workflow_path}")
        return
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_config = yaml.safe_load(f)
    
    steps = workflow_config['workflow']['steps']
    
    # 统计配置化的步骤
    config_steps = 0
    hardcode_steps = 0
    
    for step in steps:
        action = step.get('action', '')
        if action in ['handle_music', 'handle_audio_settings', 'handle_publish_schedule']:
            hardcode_steps += 1
        elif action in ['click', 'input_text', 'adjust_slider', 'select_music_item', 'input_time']:
            config_steps += 1
    
    print(f"✅ 配置化步骤: {config_steps}")
    print(f"⚠️ 硬编码步骤: {hardcode_steps}")
    
    # 2. 检查工作流引擎代码
    print("\n🔍 检查工作流引擎代码...")
    engine_path = os.path.join(
        os.path.dirname(__file__), 
        'src', 'services', 'common', 'workflow_engine.py'
    )
    
    if not os.path.exists(engine_path):
        print(f"❌ 工作流引擎文件不存在: {engine_path}")
        return
    
    with open(engine_path, 'r', encoding='utf-8') as f:
        engine_code = f.read()
    
    # 查找硬编码的element_finder调用
    hardcode_patterns = [
        r'element_finder\.find_and_click\(.*?["\'][^"\']*["\']',
        r'element_finder\.find_element\(.*?["\'][^"\']*["\']'
    ]
    
    total_hardcode = 0
    for pattern in hardcode_patterns:
        matches = re.findall(pattern, engine_code)
        total_hardcode += len(matches)
        if matches:
            print(f"⚠️ 发现 {len(matches)} 个硬编码调用:")
            for match in matches[:5]:  # 只显示前5个
                print(f"   - {match}")
            if len(matches) > 5:
                print(f"   - ... 还有 {len(matches) - 5} 个")
    
    if total_hardcode == 0:
        print("✅ 未发现硬编码的element_finder调用")
    else:
        print(f"❌ 总共发现 {total_hardcode} 个硬编码调用")
    
    # 3. 检查新的动作类型
    print("\n🆕 检查新的动作类型...")
    new_actions = [
        'adjust_slider',
        'select_music_item', 
        'input_time',
        'conditional'
    ]
    
    for action in new_actions:
        if f"_execute_{action}_action" in engine_code:
            print(f"✅ {action} 动作已实现")
        else:
            print(f"❌ {action} 动作未实现")
    
    # 4. 检查条件支持
    print("\n🔍 检查条件支持...")
    conditions_found = []
    if "selectedMusic.length > 0" in engine_code:
        conditions_found.append("音乐选择条件")
    if "keepOriginalAudio == true" in engine_code:
        conditions_found.append("保留原声条件")
    if "isScheduled == true" in engine_code:
        conditions_found.append("预定发布条件")
    
    if conditions_found:
        print(f"✅ 支持的条件: {', '.join(conditions_found)}")
    else:
        print("❌ 未找到条件支持")
    
    # 5. 生成报告
    print("\n📊 修复效果报告:")
    print(f"   - 工作流步骤总数: {len(steps)}")
    print(f"   - 配置化步骤: {config_steps}")
    print(f"   - 硬编码步骤: {hardcode_steps}")
    print(f"   - 新动作类型: {len(new_actions)}")
    print(f"   - 条件支持: {len(conditions_found)}")
    print(f"   - 剩余硬编码: {total_hardcode}")
    
    # 计算配置化程度
    if len(steps) > 0:
        config_percentage = (config_steps / len(steps)) * 100
        print(f"   - 配置化程度: {config_percentage:.1f}%")
    
    if hardcode_steps == 0 and total_hardcode == 0:
        print("\n🎉 硬编码修复完成！系统已完全配置驱动。")
    elif hardcode_steps > 0:
        print(f"\n⚠️ 还有 {hardcode_steps} 个硬编码步骤需要配置化")
    elif total_hardcode > 0:
        print(f"\n⚠️ 还有 {total_hardcode} 个硬编码调用需要移除")
    else:
        print("\n✅ 硬编码修复基本完成，系统已大幅配置化。")

if __name__ == "__main__":
    test_hardcode_fix()
