from pymongo import MongoClient
from redis import Redis
from typing import Optional, List, Dict, Any
import logging
import time
from bson.objectid import ObjectId

logger = logging.getLogger(__name__)

class SocialRedisClient:
    """社媒状态管理"""

    def __init__(self, host='localhost', port=6379):
        self.redis = Redis(host=host, port=port, decode_responses=True)

    def update_account_status(self, account_id: str, status: Dict[str, Any]) -> bool:
        """更新账号状态"""
        try:
            key = f"social:account:status:{account_id}"
            self.redis.hset(key, mapping=status)
            self.redis.set(f"social:account:heartbeat:{account_id}", int(time.time()))
            return True
        except Exception as e:
            logger.error(f"更新账号状态失败: {str(e)}")
            raise

    def get_account_status(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取账号状态"""
        try:
            key = f"social:account:status:{account_id}"
            return self.redis.hgetall(key) or None
        except Exception as e:
            logger.error(f"获取账号状态失败: {str(e)}")
            raise

class SocialDatabaseService:
    def __init__(self, db):
        """初始化社媒数据库服务

        Args:
            db: MongoDB数据库对象
        """
        self.db = db  # 直接使用传入的数据库对象
        # 不再需要 self.client[db_name]

    def get_social_apps(self,
                      filter: Optional[Dict] = None,
                      skip: int = 0,
                      limit: int = 100) -> List[Dict[str, Any]]:
        """获取社媒平台列表（已从social_apps迁移到social_platforms）"""
        try:
            filter = filter or {}
            # 直接从social_platforms集合获取数据
            platforms = list(self.db.social_platforms.find(filter).skip(skip).limit(limit))

            # 格式化返回数据
            formatted_platforms = []
            for platform in platforms:
                formatted = {
                    'id': str(platform.get('_id', '')),
                    'name': platform.get('name', '未命名平台'),
                    'platform': platform.get('id', platform.get('name', '').lower()),
                    'status': platform.get('status', 'active'),
                    'icon': platform.get('icon', '')
                }
                formatted_platforms.append(formatted)

            return formatted_platforms
        except Exception as e:
            logger.error(f"获取社媒平台列表失败: {str(e)}")
            raise

    def batch_delete_accounts(self, account_ids: List[str]) -> int:
        """批量删除账号，同时删除相关的设备关联关系"""
        try:
            # 转换账号ID为ObjectId
            object_ids = [ObjectId(id) for id in account_ids]

            # 删除账号
            result = self.db.social_accounts.delete_many(
                {"_id": {"$in": object_ids}}
            )
            deleted_count = result.deleted_count
            logger.info(f"已删除 {deleted_count} 个账号")

            # 删除相关的设备关联关系
            # 尝试不同的ID格式组合
            possible_account_ids = []
            for account_id in account_ids:
                possible_account_ids.append(account_id)
                possible_account_ids.append(ObjectId(account_id))

            # 删除设备账号映射
            mapping_result = self.db.device_account_mappings.delete_many(
                {"account_id": {"$in": possible_account_ids}}
            )
            logger.info(f"已删除 {mapping_result.deleted_count} 个设备账号关联关系")

            return deleted_count
        except Exception as e:
            logger.error(f"批量删除账号失败: {str(e)}")
            raise

    def delete_account(self, account_id: str) -> bool:
        """删除单个账号，同时删除相关的设备关联关系"""
        try:
            # 转换账号ID为ObjectId
            object_id = ObjectId(account_id)

            # 删除账号
            result = self.db.social_accounts.delete_one(
                {"_id": object_id}
            )
            deleted = result.deleted_count > 0
            logger.info(f"删除账号 {account_id} {'成功' if deleted else '失败'}")

            if deleted:
                # 删除相关的设备关联关系
                # 尝试不同的ID格式
                possible_account_ids = [account_id, object_id]

                # 删除设备账号映射
                mapping_result = self.db.device_account_mappings.delete_many(
                    {"account_id": {"$in": possible_account_ids}}
                )
                logger.info(f"已删除 {mapping_result.deleted_count} 个设备账号关联关系")

            return deleted
        except Exception as e:
            logger.error(f"删除账号失败: {str(e)}")
            raise

    def get_accounts(self,
                   app_id: str = None,
                   skip: int = 0,
                   limit: int = 100) -> List[Dict[str, Any]]:
        """获取社媒账号列表
        app_id: 可选参数，为空时返回所有账号
        """
        try:
            query = {"app_id": app_id} if app_id else {}
            return list(self.db.social_accounts.find(
                query
            ).skip(skip).limit(limit))
        except Exception as e:
            logger.error(f"获取社媒账号列表失败: {str(e)}")
            raise

    def get_accounts_by_query(self,
                            query: Dict[str, Any],
                            skip: int = 0,
                            limit: int = 100) -> List[Dict[str, Any]]:
        """根据查询条件获取社媒账号列表
        query: 查询条件，如 {"platform_id": "youtube", "core_service_id": "core1"}
        """
        try:
            logger.info(f"根据条件查询账号，原始查询: {query}")

            # 处理查询条件，确保能够处理不同格式的ID
            processed_query = {}

            # 处理平台ID
            if "platform_id" in query:
                platform_id = query["platform_id"]
                logger.info(f"处理平台ID: {platform_id}")

                # 标准化平台ID
                standard_ids = {
                    "youtube": ["youtube", "yt", "谷歌", "google"],
                    "facebook": ["facebook", "fb", "脸书"],
                    "twitter": ["twitter", "tw", "推特"],
                    "instagram": ["instagram", "ig", "ins", "照片墙"],
                    "tiktok": ["tiktok", "tt", "抖音"]
                }

                # 检查是否是标准ID
                is_standard_id = False
                for std_id, aliases in standard_ids.items():
                    if platform_id.lower() in aliases or platform_id.lower() == std_id:
                        platform_id = std_id
                        is_standard_id = True
                        logger.info(f"使用标准平台ID: {std_id}")
                        break

                if is_standard_id:
                    # 尝试查找平台
                    platform = self.db.social_platforms.find_one({"name": {"$regex": platform_id, "$options": "i"}})
                    if platform:
                        # 使用平台的_id
                        processed_query["platform_id"] = str(platform["_id"])
                        logger.info(f"找到平台: {platform.get('name', '未命名')}，标准ID: {platform_id}，使用_id: {processed_query['platform_id']}")
                    else:
                        # 尝试直接使用标准ID
                        processed_query["platform_id"] = platform_id
                        logger.info(f"未找到平台，使用标准ID: {platform_id}")
                else:
                    # 尝试查找平台
                    platform = self.db.social_platforms.find_one({"id": platform_id})
                    if platform:
                        # 使用平台的_id
                        processed_query["platform_id"] = str(platform["_id"])
                        logger.info(f"找到平台: {platform.get('name', '未命名')}，ID: {platform_id}，使用_id: {processed_query['platform_id']}")
                    else:
                        # 尝试直接使用ID
                        processed_query["platform_id"] = platform_id
                        logger.info(f"未找到平台，直接使用ID: {platform_id}")

            # 处理Core服务ID
            if "core_service_id" in query:
                core_service_id = query["core_service_id"]
                processed_query["core_service_id"] = core_service_id
                logger.info(f"使用Core服务ID: {core_service_id}")

            # 处理其他查询条件
            for key, value in query.items():
                if key not in ["platform_id", "core_service_id"]:
                    processed_query[key] = value

            logger.info(f"处理后的查询条件: {processed_query}")

            # 查询账号
            accounts = list(self.db.social_accounts.find(processed_query).skip(skip).limit(limit))
            logger.info(f"找到 {len(accounts)} 个账号")

            # 如果没有找到账号，尝试使用原始查询条件
            if not accounts and processed_query != query:
                logger.info(f"使用处理后的查询条件未找到账号，尝试使用原始查询条件: {query}")
                accounts = list(self.db.social_accounts.find(query).skip(skip).limit(limit))
                logger.info(f"使用原始查询条件找到 {len(accounts)} 个账号")

            # 处理账号数据，确保每个账号都有id字段
            for account in accounts:
                if "_id" in account and ("id" not in account or not account["id"]):
                    account["id"] = str(account["_id"])
                    logger.info(f"为账号添加id字段: {account['id']}")

            return accounts
        except Exception as e:
            logger.error(f"根据条件查询账号失败: {str(e)}")
            raise

    def count_accounts(self, query: Dict[str, Any]) -> int:
        """获取符合条件的账号总数

        Args:
            query: 查询条件，如 {"platform_id": "youtube", "core_service_id": "core1"}

        Returns:
            符合条件的账号总数
        """
        try:
            logger.info(f"计算符合条件的账号总数，原始查询: {query}")

            # 处理查询条件，确保能够处理不同格式的ID
            processed_query = {}

            # 处理平台ID
            if "platform_id" in query:
                platform_id = query["platform_id"]
                logger.info(f"处理平台ID: {platform_id}")

                # 标准化平台ID
                standard_ids = {
                    "youtube": ["youtube", "yt", "谷歌", "google"],
                    "facebook": ["facebook", "fb", "脸书"],
                    "twitter": ["twitter", "tw", "推特"],
                    "instagram": ["instagram", "ig", "ins", "照片墙"],
                    "tiktok": ["tiktok", "tt", "抖音"]
                }

                # 检查是否是标准ID
                is_standard_id = False
                for std_id, aliases in standard_ids.items():
                    if platform_id.lower() in aliases or platform_id.lower() == std_id:
                        platform_id = std_id
                        is_standard_id = True
                        logger.info(f"使用标准平台ID: {std_id}")
                        break

                if is_standard_id:
                    # 尝试查找平台
                    platform = self.db.social_platforms.find_one({"name": {"$regex": platform_id, "$options": "i"}})
                    if platform:
                        # 使用平台的_id
                        processed_query["platform_id"] = str(platform["_id"])
                        logger.info(f"找到平台: {platform.get('name', '未命名')}，标准ID: {platform_id}，使用_id: {processed_query['platform_id']}")
                    else:
                        # 尝试直接使用标准ID
                        processed_query["platform_id"] = platform_id
                        logger.info(f"未找到平台，使用标准ID: {platform_id}")
                else:
                    # 尝试查找平台
                    platform = self.db.social_platforms.find_one({"id": platform_id})
                    if platform:
                        # 使用平台的_id
                        processed_query["platform_id"] = str(platform["_id"])
                        logger.info(f"找到平台: {platform.get('name', '未命名')}，ID: {platform_id}，使用_id: {processed_query['platform_id']}")
                    else:
                        # 尝试直接使用ID
                        processed_query["platform_id"] = platform_id
                        logger.info(f"未找到平台，直接使用ID: {platform_id}")

            # 处理Core服务ID
            if "core_service_id" in query:
                core_service_id = query["core_service_id"]
                processed_query["core_service_id"] = core_service_id
                logger.info(f"使用Core服务ID: {core_service_id}")

            # 处理其他查询条件
            for key, value in query.items():
                if key not in ["platform_id", "core_service_id"]:
                    processed_query[key] = value

            logger.info(f"处理后的查询条件: {processed_query}")

            # 计算总数
            count = self.db.social_accounts.count_documents(processed_query)
            logger.info(f"符合条件的账号总数: {count}")

            # 如果没有找到账号，尝试使用原始查询条件
            if count == 0 and processed_query != query:
                logger.info(f"使用处理后的查询条件未找到账号，尝试使用原始查询条件: {query}")
                count = self.db.social_accounts.count_documents(query)
                logger.info(f"使用原始查询条件找到的账号总数: {count}")

            return count
        except Exception as e:
            logger.error(f"计算符合条件的账号总数失败: {str(e)}")
            raise

    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取单个社媒账号"""
        try:
            return self.db.social_accounts.find_one({"_id": ObjectId(account_id)})
        except Exception as e:
            logger.error(f"获取社媒账号失败: {str(e)}")
            raise

    def get_account_by_id(self, account_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取账号

        Args:
            account_id: 账号ID

        Returns:
            账号数据或None
        """
        try:
            from bson import ObjectId

            # 尝试将ID转换为ObjectId
            try:
                obj_id = ObjectId(account_id)
                account = self.db.social_accounts.find_one({"_id": obj_id})
                if account:
                    return account
            except Exception:
                # 如果转换失败，尝试直接使用ID字符串查询
                pass

            # 尝试使用id字段查询
            return self.db.social_accounts.find_one({"id": account_id})
        except Exception as e:
            logger.error(f"根据ID获取账号失败: {str(e)}")
            raise

    def create_account(self, account_data: Dict[str, Any]) -> str:
        """创建社媒账号"""
        try:
            result = self.db.social_accounts.insert_one(account_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建社媒账号失败: {str(e)}")
            raise

    def update_account(self, account_id: str, update_data: Dict[str, Any]) -> bool:
        """更新社媒账号"""
        try:
            result = self.db.social_accounts.update_one(
                {"_id": ObjectId(account_id)},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新社媒账号失败: {str(e)}")
            raise

    def create_post(self, post_data: Dict[str, Any]) -> str:
        """创建社媒内容"""
        try:
            result = self.db.social_posts.insert_one(post_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建社媒内容失败: {str(e)}")
            raise

    def get_posts(self,
                account_id: str,
                skip: int = 0,
                limit: int = 100) -> List[Dict[str, Any]]:
        """获取社媒内容列表"""
        try:
            return list(self.db.social_posts.find(
                {"account_id": account_id}
            ).skip(skip).limit(limit))
        except Exception as e:
            logger.error(f"获取社媒内容列表失败: {str(e)}")
            raise

    def get_analytics(self,
                    app_id: str,
                    account_id: str = None,
                    start_date: str = None,
                    end_date: str = None) -> List[Dict[str, Any]]:
        """获取社媒分析数据"""
        try:
            query = {"app_id": app_id}
            if account_id:
                query["account_id"] = account_id
            if start_date and end_date:
                query["date"] = {"$gte": start_date, "$lte": end_date}

            return list(self.db.social_analytics.find(query))
        except Exception as e:
            logger.error(f"获取社媒分析数据失败: {str(e)}")
            raise

    def record_analytics(self, analytics_data: Dict[str, Any]) -> str:
        """记录社媒分析数据"""
        try:
            result = self.db.social_analytics.insert_one(analytics_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"记录社媒分析数据失败: {str(e)}")
            raise

    # 新增账号分组管理功能
    def create_group(self, group_data: Dict[str, Any]) -> str:
        """创建账号分组"""
        try:
            result = self.db.social_groups.insert_one(group_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建账号分组失败: {str(e)}")
            raise

    def add_account_to_group(self, group_id: str, account_id: str) -> bool:
        """添加账号到分组"""
        try:
            result = self.db.social_groups.update_one(
                {"_id": ObjectId(group_id)},
                {"$addToSet": {"account_ids": account_id}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"添加账号到分组失败: {str(e)}")
            raise

    def get_group_accounts(self, group_id: str) -> List[Dict[str, Any]]:
        """获取分组下的账号列表"""
        try:
            group = self.db.social_groups.find_one({"_id": ObjectId(group_id)})
            if not group:
                return []

            account_ids = group.get("account_ids", [])
            return list(self.db.social_accounts.find({
                "_id": {"$in": [ObjectId(id) for id in account_ids]}
            }))
        except Exception as e:
            logger.error(f"获取分组账号失败: {str(e)}")
            raise

    def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建发布任务"""
        try:
            # 设置默认状态
            task_data.setdefault("status", "pending")
            task_data.setdefault("created_at", time.time())

            result = self.db.social_tasks.insert_one(task_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建发布任务失败: {str(e)}")
            raise

    def get_workflows(self,
                    skip: int = 0,
                    limit: int = 100) -> List[Dict[str, Any]]:
        """获取工作流列表"""
        try:
            workflows = list(self.db.social_workflows.find().skip(skip).limit(limit))
            return [{
                "id": str(w["_id"]),
                "name": w["name"],
                "description": w.get("description", ""),
                "created_at": w["created_at"]
            } for w in workflows]
        except Exception as e:
            logger.error(f"获取工作流列表失败: {str(e)}")
            raise

    def create_workflow(self, workflow_data: Dict[str, Any]) -> str:
        """创建工作流"""
        try:
            workflow_data.setdefault("created_at", time.time())
            result = self.db.social_workflows.insert_one(workflow_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"创建工作流失败: {str(e)}")
            raise

    def batch_update_accounts(self, account_ids: List[str], update_data: Dict[str, Any]) -> int:
        """批量更新账号"""
        try:
            result = self.db.social_accounts.update_many(
                {"_id": {"$in": [ObjectId(id) for id in account_ids]}},
                {"$set": update_data}
            )
            return result.modified_count
        except Exception as e:
            logger.error(f"批量更新账号失败: {str(e)}")
            raise

    def import_accounts_from_text(self, text_content: str, platform_mapping=None, core_service_id="default") -> Dict[str, Any]:
        """从文本导入账号

        Args:
            text_content: 文本内容
            platform_mapping: 平台映射，如 {"GG": "youtube", "FB": "facebook"}
            core_service_id: Core服务ID

        Returns:
            导入结果统计
        """
        logger.info(f"开始导入账号，平台映射: {platform_mapping}, Core服务ID: {core_service_id}")

        if platform_mapping is None:
            platform_mapping = {
                "GG": "youtube",
                "FB": "facebook"
            }
            logger.info(f"使用默认平台映射: {platform_mapping}")

        # 检查数据库连接
        try:
            collections = self.db.list_collection_names()
            logger.info(f"数据库集合列表: {collections}")
            if 'social_accounts' not in collections:
                logger.warning("数据库中不存在social_accounts集合，将自动创建")
        except Exception as db_error:
            logger.error(f"检查数据库连接失败: {str(db_error)}")
            raise

        lines = text_content.strip().split("\n")
        logger.info(f"导入文本共 {len(lines)} 行")

        imported_count = 0
        errors = []

        current_display_name = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是账号标识行
            if not any(p + "：" in line or p + ":" in line for p in platform_mapping.keys()):
                # 可能是显示名称行
                current_display_name = line
                continue

            try:
                # 解析平台和账号信息
                platform_code = None
                for code in platform_mapping.keys():
                    if code + "：" in line or code + ":" in line:
                        platform_code = code
                        break

                if not platform_code:
                    continue

                # 获取平台ID
                platform_id = platform_mapping[platform_code]
                logger.info(f"解析平台代码 {platform_code} 对应平台ID: {platform_id}")

                # 尝试查找平台
                platform = None
                try:
                    # 先尝试使用标准ID查找
                    platform = self.db.social_platforms.find_one({"name": {"$regex": platform_id, "$options": "i"}})
                    if platform:
                        logger.info(f"找到平台: {platform.get('name', '未命名')}, ID: {platform_id}")
                    else:
                        # 尝试使用ID字段查找
                        platform = self.db.social_platforms.find_one({"id": platform_id})
                        if platform:
                            logger.info(f"通过ID字段找到平台: {platform.get('name', '未命名')}")
                        else:
                            logger.warning(f"未找到平台: {platform_id}，将使用原始ID")
                except Exception as platform_error:
                    logger.error(f"查找平台失败: {str(platform_error)}")

                # 分割账号信息
                if platform_code + "：" in line:
                    account_part = line.split(platform_code + "：")[1]
                else:
                    account_part = line.split(platform_code + ":")[1]

                logger.info(f"账号信息部分: {account_part}")

                # 处理不同的分隔符
                if "----" in account_part:
                    parts = account_part.split("----")
                elif "|" in account_part:
                    parts = account_part.split("|")
                else:
                    parts = [account_part]

                username = parts[0].strip()

                # 创建账号数据
                account_data = {
                    "id": f"{platform_id}_{username}_{int(time.time())}",  # 生成唯一ID
                    "username": username,
                    "core_service_id": core_service_id,
                    "status": "active",
                    "created_at": time.time(),
                    "updated_at": time.time()
                }

                logger.info(f"生成唯一ID: {account_data['id']}")

                # 设置平台ID
                if platform:
                    # 如果找到了平台，使用平台的_id
                    account_data["platform_id"] = str(platform["_id"])
                    logger.info(f"使用平台_id: {account_data['platform_id']}")
                else:
                    # 否则使用映射的ID
                    account_data["platform_id"] = platform_id
                    logger.info(f"使用映射的平台ID: {platform_id}")

                # 添加密码
                if len(parts) > 1:
                    account_data["password"] = parts[1].strip()

                # 添加恢复邮箱
                if len(parts) > 2:
                    account_data["recovery_email"] = parts[2].strip()

                # 添加恢复码
                if len(parts) > 3:
                    account_data["recovery_code"] = parts[3].strip()

                # 添加备注/描述
                if len(parts) > 4:
                    account_data["description"] = parts[4].strip()

                # 添加显示名称
                if current_display_name:
                    account_data["display_name"] = current_display_name

                # 检查账号是否已存在
                try:
                    # 首先尝试使用username和platform_id查询
                    query = {
                        "username": username
                    }

                    # 使用相同的平台ID查询方式
                    if platform:
                        query["platform_id"] = str(platform["_id"])
                    else:
                        query["platform_id"] = platform_id

                    logger.info(f"查询账号是否存在: {query}")
                    existing = self.db.social_accounts.find_one(query)

                    # 如果没有找到，尝试使用id查询
                    if not existing and "id" in account_data:
                        id_query = {"id": account_data["id"]}
                        logger.info(f"使用ID查询账号: {id_query}")
                        existing = self.db.social_accounts.find_one(id_query)

                    if existing:
                        # 更新现有账号
                        logger.info(f"找到现有账号，ID: {existing['_id']}，准备更新")

                        # 保留现有账号的ID
                        if "id" in existing:
                            account_data["id"] = existing["id"]
                            logger.info(f"保留现有账号ID: {account_data['id']}")

                        result = self.db.social_accounts.update_one(
                            {"_id": existing["_id"]},
                            {"$set": account_data}
                        )
                        logger.info(f"更新账号结果: matched={result.matched_count}, modified={result.modified_count}")
                    else:
                        # 创建新账号
                        logger.info(f"未找到现有账号，准备创建新账号: {account_data}")

                        # 确保有唯一ID
                        if "id" not in account_data or not account_data["id"]:
                            account_data["id"] = f"{platform_id}_{username}_{int(time.time())}"
                            logger.info(f"生成新的唯一ID: {account_data['id']}")

                        result = self.db.social_accounts.insert_one(account_data)
                        logger.info(f"创建账号结果: inserted_id={result.inserted_id}")

                    imported_count += 1
                    logger.info(f"成功导入账号: {username}，当前已导入 {imported_count} 个账号")
                except Exception as save_error:
                    error_msg = f"保存账号 {username} 失败: {str(save_error)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    continue

            except Exception as e:
                errors.append(f"导入行 '{line}' 失败: {str(e)}")

        result = {
            "imported_count": imported_count,
            "errors": errors
        }

        logger.info(f"账号导入完成，结果: {result}")
        return result

    def import_accounts_batch(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量导入账号

        Args:
            accounts: 账号数据列表

        Returns:
            导入结果统计
        """
        logger.info(f"开始批量导入账号，共 {len(accounts)} 个账号")

        # 检查数据库连接
        try:
            collections = self.db.list_collection_names()
            logger.info(f"数据库集合列表: {collections}")
            if 'social_accounts' not in collections:
                logger.warning("数据库中不存在social_accounts集合，将自动创建")
        except Exception as db_error:
            logger.error(f"检查数据库连接失败: {str(db_error)}")
            raise

        imported_count = 0
        errors = []
        platform_not_found = set()  # 记录未找到的平台ID

        # 遍历账号数据
        for account_data in accounts:
            try:
                username = account_data.get("username")
                platform_id = account_data.get("platform_id")

                if not username or not platform_id:
                    errors.append(f"账号数据缺少必要字段: {account_data}")
                    continue

                # 添加创建和更新时间
                if "created_at" not in account_data:
                    account_data["created_at"] = time.time()
                if "updated_at" not in account_data:
                    account_data["updated_at"] = time.time()

                # 确保recovery_code字段被正确处理
                if "recovery_code" in account_data:
                    recovery_code = account_data["recovery_code"]
                    # 记录原始值，便于调试
                    logger.info(f"账号 {username} 的恢复码原始值: {recovery_code}")
                    # 确保恢复码是字符串类型
                    account_data["recovery_code"] = str(recovery_code).strip()
                    logger.info(f"账号 {username} 的恢复码处理后值: {account_data['recovery_code']}")

                # 尝试将platform_id转换为social_platforms表中的_id
                original_platform_id = platform_id
                platform = None

                # 先尝试使用标准ID查找
                platform = self.db.social_platforms.find_one({"name": {"$regex": platform_id, "$options": "i"}})
                if not platform:
                    # 尝试使用ID字段查找
                    platform = self.db.social_platforms.find_one({"id": platform_id})

                if platform:
                    # 如果找到了平台，使用平台的_id
                    account_data["platform_id"] = str(platform["_id"])
                    logger.info(f"找到平台: {platform.get('name', '未命名')}，将字符串ID '{original_platform_id}' 转换为平台_id: {account_data['platform_id']}")
                else:
                    # 如果未找到平台，记录错误并跳过此账号
                    platform_not_found.add(platform_id)
                    error_msg = f"未找到平台: {platform_id}，请先添加该平台"
                    logger.warning(error_msg)
                    errors.append(f"账号 {username} 导入失败: {error_msg}")
                    continue

                # 检查账号是否已存在
                existing = self.db.social_accounts.find_one({
                    "username": username,
                    "platform_id": account_data["platform_id"]
                })

                try:
                    if existing:
                        # 更新现有账号
                        self.db.social_accounts.update_one(
                            {"_id": existing["_id"]},
                            {"$set": account_data}
                        )
                        logger.info(f"更新账号: {username}")
                    else:
                        # 创建新账号
                        # 确保有唯一ID
                        if "id" not in account_data or not account_data["id"]:
                            account_data["id"] = f"{original_platform_id}_{username}_{int(time.time())}"
                            logger.info(f"生成新的唯一ID: {account_data['id']}")

                        self.db.social_accounts.insert_one(account_data)
                        logger.info(f"创建账号: {username}")

                    imported_count += 1
                except Exception as save_error:
                    error_msg = f"保存账号 {username} 失败: {str(save_error)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    continue

            except Exception as e:
                errors.append(f"处理账号数据失败: {str(e)}, 数据: {account_data}")

        # 如果有未找到的平台，添加到错误信息中
        if platform_not_found:
            platforms_str = ", ".join(platform_not_found)
            errors.append(f"以下平台未在系统中找到，请先添加这些平台: {platforms_str}")

        result = {
            "imported_count": imported_count,
            "errors": errors
        }

        logger.info(f"批量导入账号完成，结果: {result}")
        return result

    def get_devices_by_core(self, core_service_id: str) -> List[Dict[str, Any]]:
        """获取Core服务管理的设备列表"""
        try:
            return list(self.db.devices.find({"core_id": core_service_id}))
        except Exception as e:
            logger.error(f"获取Core服务设备列表失败: {str(e)}")
            raise

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息

        Args:
            task_id: 任务ID

        Returns:
            任务信息或None
        """
        try:
            # 尝试使用ObjectId查询
            try:
                obj_id = ObjectId(task_id)
                task = self.db.social_tasks.find_one({"_id": obj_id})
                if task:
                    return task
            except Exception:
                # 如果转换失败，尝试直接使用ID字符串查询
                pass

            # 尝试使用task_id字段查询
            task = self.db.social_tasks.find_one({"task_id": task_id})
            if task:
                return task

            return None
        except Exception as e:
            logger.error(f"获取任务信息失败: {str(e)}")
            raise

    def update_task(self, task_id: str, update_data: Dict[str, Any]) -> bool:
        """更新任务信息

        Args:
            task_id: 任务ID
            update_data: 更新数据

        Returns:
            是否成功
        """
        try:
            # 尝试使用ObjectId查询
            try:
                obj_id = ObjectId(task_id)
                result = self.db.social_tasks.update_one(
                    {"_id": obj_id},
                    {"$set": update_data}
                )
                if result.modified_count > 0:
                    return True
            except Exception:
                # 如果转换失败，尝试直接使用ID字符串查询
                pass

            # 尝试使用task_id字段查询
            result = self.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新任务信息失败: {str(e)}")
            raise
