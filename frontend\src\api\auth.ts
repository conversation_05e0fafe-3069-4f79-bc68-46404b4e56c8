import { authSocket } from '@/socket.io'
import request from '@/utils/request'

export interface LoginParams {
  username: string
  password: string
}

export interface AuthResponse {
  token: string
  user: any
}

interface TokenResponse {
  access_token: string
  token_type: string
}

export const login = async (credentials: LoginParams): Promise<AuthResponse> => {
  const formData = new URLSearchParams()
  formData.append('username', credentials.username)
  formData.append('password', credentials.password)

  try {
    const response = await request.post<TokenResponse>('/auth/token', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    
    const token = response.data.access_token
    if (!token) {
      throw new Error('登录失败: 无效的token')
    }

    localStorage.setItem('token', token)
    
    const userResponse = await request.get('/auth/users/me')
    return {
      token,
      user: userResponse.data
    }
  } catch (error) {
    localStorage.removeItem('token')
    throw error
  }
}

export const logout = async (): Promise<void> => {
  try {
    const response = await request.post('/auth/logout')
    if (response.status !== 200) {
      throw new Error('退出失败')
    }
    authSocket.disconnect()
  } catch (error) {
    console.error('退出请求失败:', error)
    throw error
  } finally {
    localStorage.removeItem('token')
  }
}

export const getCurrentUser = async (): Promise<any> => {
  return await request.get('/auth/users/me')
}