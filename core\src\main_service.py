"""
Core主服务
负责管理设备和服务生命周期
"""

import os
import logging
import yaml
import asyncio
from typing import Dict, List, Any, Optional

from src.devices.ldplayer.manager import LDPlayerManager
from src.devices.base import DeviceInfo
from src.services.task_executor import TaskExecutor

logger = logging.getLogger(__name__)

class CoreMainService:
    """Core主服务类"""

    def __init__(self, config_path: Optional[str] = None, settings = None):
        """初始化Core主服务

        Args:
            config_path: 配置文件路径
            settings: 配置对象，如果提供则优先使用
        """
        self.config_path = config_path
        self.settings = settings  # 使用传入的settings对象
        self.ldplayer_manager = None
        self.task_executor = None
        self.is_initialized = False
        self.device_configs = {}

        logger.info("Core主服务初始化")

    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 加载配置
            await self._load_config()

            # 从settings获取ldconsole_path（如果可用）
            ldconsole_path = None
            if self.settings:
                ldconsole_path = self.settings.ldconsole_path
                logger.info(f"从settings获取ldconsole_path: {ldconsole_path}")
            else:
                # 否则从device_configs获取
                ldconsole_path = self.device_configs.get('ldconsole_path')
                logger.info(f"从device_configs获取ldconsole_path: {ldconsole_path}")

            if not ldconsole_path:
                logger.error("配置中缺少ldconsole_path")
                return False

            # 检查ldconsole.exe是否存在
            if not os.path.exists(ldconsole_path):
                logger.error(f"ldconsole.exe不存在: {ldconsole_path}")
                logger.info("请在core_config.yaml中配置正确的ldconsole_path路径")
                return False

            self.ldplayer_manager = LDPlayerManager(ldconsole_path)
            init_success = await self.ldplayer_manager.initialize()
            if not init_success:
                logger.error("初始化雷电模拟器管理器失败")
                return False

            # 初始化任务执行器
            self.task_executor = TaskExecutor(self)
            task_executor_init = await self.task_executor.initialize()
            if not task_executor_init:
                logger.error("初始化任务执行器失败")
                return False

            self.is_initialized = True
            logger.info("Core主服务初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化Core主服务异常: {str(e)}", exc_info=True)
            return False

    async def _load_config(self) -> None:
        """加载配置"""
        # 默认配置
        self.device_configs = {
            'ldconsole_path': 'C:\\LDPlayer\\LDPlayer9\\ldconsole.exe',
            'auto_start_devices': []
        }

        # 如果提供了配置文件，从文件加载配置
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                if config and isinstance(config, dict):
                    # 从devices部分获取配置
                    if 'devices' in config:
                        devices_config = config['devices']
                        if 'ldconsole_path' in devices_config:
                            self.device_configs['ldconsole_path'] = devices_config['ldconsole_path']
                        if 'auto_start_devices' in devices_config:
                            self.device_configs['auto_start_devices'] = devices_config['auto_start_devices']

                logger.info(f"从文件{self.config_path}加载配置成功")

            except Exception as e:
                logger.error(f"从文件{self.config_path}加载配置失败: {str(e)}", exc_info=True)

    async def get_device_list(self) -> List[DeviceInfo]:
        """获取设备列表"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return []

        return await self.ldplayer_manager.get_all_devices()

    async def start_device(self, device_id: str) -> bool:
        """启动设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.start_device(device_id)

    async def stop_device(self, device_id: str) -> bool:
        """停止设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.stop_device(device_id)

    async def restart_device(self, device_id: str) -> bool:
        """重启设备"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return False

        return await self.ldplayer_manager.restart_device(device_id)

    async def execute_device_command(self, device_id: str, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行设备命令"""
        if not self.is_initialized or not self.ldplayer_manager:
            logger.error("Core主服务未初始化")
            return {"success": False, "error": "Core主服务未初始化"}

        device = self.ldplayer_manager.devices.get(device_id)
        if not device:
            logger.error(f"设备{device_id}不存在")
            return {"success": False, "error": f"设备{device_id}不存在"}

        return await device.execute_command(command, params or {})

    async def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.create_task(task_data)

    async def start_task(self, task_id: str) -> bool:
        """开始执行任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.start_task(task_id)

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.pause_task(task_id)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return False

        return await self.task_executor.cancel_task(task_id)

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return None

        return await self.task_executor.get_task_status(task_id)

    async def get_task_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志"""
        if not self.is_initialized or not self.task_executor:
            logger.error("Core主服务未初始化")
            return []

        return await self.task_executor.get_task_logs(task_id)

    async def shutdown(self) -> None:
        """关闭服务"""
        if self.ldplayer_manager:
            try:
                await self.ldplayer_manager.shutdown()
            except Exception as e:
                logger.error(f"关闭雷电模拟器管理器异常: {str(e)}", exc_info=True)

        if self.task_executor:
            try:
                await self.task_executor.shutdown()
            except Exception as e:
                logger.error(f"关闭任务执行器异常: {str(e)}", exc_info=True)

        self.is_initialized = False
        logger.info("Core主服务已关闭")

    async def stop(self) -> None:
        """停止服务（用于热重载）"""
        logger.info("正在停止Core主服务（热重载）...")
        await self.shutdown()
        logger.info("Core主服务已停止（热重载）")

