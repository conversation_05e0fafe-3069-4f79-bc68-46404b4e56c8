# YouTube 批量上传功能架构设计

## 1. 概述

本文档描述 ThunderHub 项目中 YouTube 批量上传功能的架构设计，涵盖文件夹管理、模拟器上传流程、数据存储、API 设计、性能优化和容错机制。该功能基于 ThunderHub 的多 Core 架构，利用现有技术栈（FastAPI、MongoDB、Redis、Consul、Traefik、Appium、ldconsole、Frida 等）实现高效、可靠的批量上传。任务通过 Core 服务执行，上传状态由 Core 服务通过 Appium、Frida 并回传至后端。

## 2. 架构图

```mermaid
graph TD
    A[前端: Vue3] -->|HTTPS/WebSocket| B[后端: FastAPI]
    B -->|gRPC/REST| C[Core 服务: Windows 主机]
    B -->|任务分发| D[Redis: Celery 队列]
    D --> C
    C -->|ADB/ldconsole| F[雷电模拟器集群]
    F -->|Appium/Frida| G[YouTube 应用]
    G -->|状态检测| C
    C -->|状态回传| H[Redis: 状态存储]
    H -->|WebSocket| B
    B -->|WebSocket| A
    B -->|数据存储| I[(MongoDB)]
    B -->|服务发现| J[Consul]
    B -->|负载均衡| K[Traefik]
    B -->|监控| L[Prometheus + Grafana]
    C -->|错误重试| M[Kafka: 错误队列]
```

- 关键说明：
  任务执行：
  - 任务从 Redis（Celery 队列）分发到 Core 服务，Core 服务通过 ADB/ldconsole/Appium 控制雷电模拟器和 YouTube 应用。

  状态检测：

  - YouTube 应用状态由 Core 服务通过 Appium（UI 检测）、Frida（运行时监控）和 Logcat（错误日志）捕获

  - 状态由 Core 服务推送到 Redis，后端通过 WebSocket（/device-socket.io）推送至前端。

  一致性：
  - 任务和状态均通过 Core 服务处理，符合 ThunderHub 多 Core 架构，使用 Consul（服务发现）和 Traefik（负载均衡）。

## 3. 文件夹管理流程

1. 用户通过前端指定视频(Core 所在服务器的)文件夹路径
2. 后端（FastAPI）验证路径有效性：
   - 检查路径是否存在
   - 验证是否包含有效视频文件（MP4、MOV 等）
   - 使用异步验证（asyncio）支持大文件夹增量扫描。
3. 有效路径存储到 MongoDB 的 uploads 集合，生成唯一任务 ID。
4. 模拟器通过以下方式访问文件：
   - ADB push 到模拟器共享目录
   - 或直接挂载主机目录

## 4. 数据存储设计

### MongoDB Schema (uploads 集合)

```json
{
  "_id": "ObjectId",
  "task_id": "String", // 唯一任务 ID
  "source": {
    "type": "local | minio | s3", // 支持 MinIO 分布式存储
    "path": "String" // 本地路径或 MinIO/S3 URL
  },
  "video_files": [
    {
      "file_id": "String", // 文件唯一标识，兼容 MinIO
      "name": "String",
      "status": "String", // pending, processing, completed, failed, retrying
      "progress": "Number", // 0-100
      "youtube_id": "String", // YouTube 视频 ID
      "error_message": "String", // 错误信息
      "retry_count": "Number" // 重试次数
    }
  ],
  "user_id": "ObjectId", // 用户 ID，关联 RBAC
  "account_id": "String", // YouTube 账号 ID
  "metadata": {
    "default_title": "String",
    "default_description": "String",
    "privacy_status": "String" // public, private, unlisted
  },
  "priority": "Number", // 任务优先级，兼容 Celery
  "device_id": "String", // 可选，指定设备
  "created_at": "ISODate",
  "updated_at": "ISODate",
  "status": "String" // pending, processing, completed, failed
}
```

## 5. 模拟器上传流程

### 5.1 任务调度

1. **任务创建**：
   - 后端（FastAPI）创建上传任务
   - 推送到Redis（Celery队列）

2. **任务分发**：
   - Celery Worker通过gRPC/REST分发任务
   - 使用Consul服务发现和Traefik路由

### 5.2 文件传输

**传输方式**：

- Core服务从MinIO或本地路径获取文件
- 通过ADB传输到模拟器：

```bash
adb push /host/path/*.mp4 /sdcard/Download
```

**优化措施**：

- 批量传输减少ADB调用
- 缓存常用文件
- 定期清理（ldconsole）

### 5.3 YouTube上传控制

1. **应用启动**：
   - 通过Appium控制YouTube应用
   - 支持多账号切换

2. **上传执行**：
   - 选择目标文件
   - 提交上传

3. **元数据设置**：
   - 使用YouTube Data API设置：
     - 标题
     - 描述
     - 隐私状态

### 5.4 状态检测机制

1. **Appium检测**：
   - 监控UI元素（进度条、提示文本）

2. **Frida监控**：
   - 拦截网络请求
   - 捕获上传进度和错误

3. **API验证**：
   - 通过YouTube Data API验证视频状态

4. **日志分析**：
   - Logcat捕获错误日志

### 5.5 状态回传流程

1. **状态整合**：
   - Core服务收集各渠道状态
   - 推送到Redis

2. **前端通知**：
   - 后端从Redis获取状态
   - 通过WebSocket推送至前端
   - 使用permessage-deflate压缩和TLS加密

### 5.6 资源清理

- 通过ldconsole清理模拟器缓存
- 防止存储空间溢出

## 6. API 设计

### POST /api/v1/validate-folder

验证文件夹路径或云存储有效性。

请求：

```json
{
  "source": {
    "type": "local | minio | s3",
    "path": "/path/to/videos | minio://bucket/videos"
  }
}
```

响应：

```json
{
  "valid": true,
  "files": [
    {
      "file_id": "uuid-123",
      "name": "video1.mp4",
      "size": ********,
      "last_modified": "ISODate"
    }
  ],
  "message": "验证成功"
}
```

### POST /api/v1/uploads

创建上传任务

请求：

```json
{
  "source": {
    "type": "local | minio | s3",
    "path": "/path/to/videos | minio://bucket/videos"
  },
  "account_id": "youtube_account_123",
  "metadata": {
    "default_title": "视频标题",
    "default_description": "视频描述",
    "privacy_status": "private",
    "custom_metadata": [
      {
        "file_name": "video1.mp4",
        "title": "自定义标题",
        "description": "自定义描述"
      }
    ]
  },
  "priority": 1,
  "device_id": "device-123"
}
```

响应：

```json
{
  "task_id": "upload_task_456",
  "status": "pending",
  "estimated_time": 3600
}
```

GET /api/v1/uploads/:task_id

查询任务状态，支持 GraphQL。

响应：

```json
{
  "task_id": "upload_task_456",
  "status": "processing",
  "progress": 75,
  "video_files": [
    {
      "file_id": "uuid-123",
      "name": "video1.mp4",
      "status": "completed",
      "youtube_id": "youtube_video_123"
    },
    {
      "file_id": "uuid-456",
      "name": "video2.mp4",
      "status": "processing",
      "progress": 50
    }
  ]
}
```

GraphQL 查询示例：

query {
  upload(taskId: "upload_task_456") {
    status
    progress
    videoFiles {
      fileId
      name
      status
      youtubeId
    }
  }
}

## 7. 错误处理

| 错误类型 | 处理方式 |
|---------|---------|
|无效文件夹路径|返回 400 错误，提示用户检查路径，记录到 Sentry|
|无有效视频文件|返回 400 错误，列出文件内容，建议支持的格式|
|模拟器连接失败|Core 服务重试 3 次，失败后推送到 Kafka 错误队列|
|YouTube 上传失败|Core 服务通过 Frida/Logcat 捕获错误，标记failed，推送到 Kafka|
|网络中断|Core 服务通过 Frida 检测，记录进度到 Redis，支持断点续传|

## 8. 与现有系统集成

### 认证与用户管理

- 使用 ThunderHub 的 JWT 认证和 RBAC，限制文件夹访问
- 敏感操作（如上传）需 MFA 验证

### 设备管理

- Core 服务复用设备连接池和 ldconsole 协议，管理雷电模拟器
- Consul 动态发现 Core 实例，Traefik 负载均衡

### 任务调度

- Celery 分布式任务队列，分发任务到 Core 服务
- 支持 TaskPlugin 扩展，预留 AI 决策接口

### 文件管理

- 集成 MinIO 分布式存储，支持高并发访问
- 使用 Elasticsearch 提供文件检索（规划中）

### 社媒应用管理

- YouTube 账号管理复用社媒模块，账号数据 AES 加密

### 状态反馈

- Core 服务通过 Appium、Frida 和 Logcat 捕获状态
- 状态推送到 Redis，后端通过 WebSocket（/device-socket.io）推送

### 可观测性

- 集成 Prometheus、Grafana、Loki、Jaeger
- OpenTelemetry 追踪 API 和任务执行

## 9. 测试策略

### 单元测试

- 文件夹验证逻辑（pytest）
- API 请求处理（FastAPI 测试客户端）
- 状态检测逻辑（Appium、Frida）

### 集成测试

- 文件传输到模拟器（ADB/ldconsole）
- Appium/Frida 与 YouTube 应用的交互
- Core 服务状态回传（Redis、WebSocket）

### E2E 测试

- 从前端到 YouTube 上传全流程（Cypress）
- 模拟网络中断、API 限额、上传失败场景

### 性能测试

- 多模拟器并发上传（Locust）
- 大文件夹处理效率（>1000 文件）

### 回归测试

- 自动化测试覆盖新功能（Vitest、Cypress）

## 10. 部署要求

### 环境配置

- 前端：Docker 容器（Node.js 镜像），端口 5713
- 后端：Docker 容器（FastAPI），端口 8000
- Core：Windows 主机，运行 core/main.py，注册到 Consul
- 数据库：MongoDB 5.0+（分片支持），Redis 7.0
- 服务发现：Consul
- 负载均衡：Traefik
- 其他：ADB 环境、雷电模拟器、YouTube 开发者账号、Frida 环境

### 硬件要求

- 每模拟器：4 核 CPU、8GB 内存、SSD 存储
- Core 主机：16 核 CPU、32GB 内存

### 安全性

- YouTube 账号凭证 AES 加密，存储在 MongoDB
- WebSocket 使用 TLS 加密
- Frida 操作受权限控制（frida_config.yaml）

### 部署方式

- 前端/后端：Kubernetes 集群，CI/CD 通过 GitHub Actions
- Core：Windows 主机手动部署，scripts/setup.ps1 初始化
- 一键启动：docker-compose up（前端、后端、MongoDB、Redis、Consul）

## 11. 性能优化

### 文件传输

- 批量传输文件，缓存常用文件到模拟器

### 并发处理

- Core 服务支持多模拟器并发，Traefik 负载均衡
- Celery 动态分配任务，基于优先级和设备负载

### API 效率

- Redis 缓存元数据模板和任务状态

### 状态检测

- Appium 和 Frida 批量检测状态，减少轮询频率
- WebSocket 消息压缩（permessage-deflate）

### 断点续传

- Core 服务记录上传进度到 Redis，网络中断后恢复

## 12. 未来扩展方向

- 多平台支持：扩展到 TikTok、Instagram（复用社媒模块）
- AI 辅助：使用 ONNX 模型生成标题、描述（任务调度扩展）
- 数据分析：集成 YouTube Analytics API，提供视频性能报告
- 移动端支持：开发 Vue3 移动端应用，远程任务管理

## 13. 项目结构

```项目结构
backend/
├── app/
│   ├── api/
│   │   ├── v1/
│   │   │   ├── uploads.py        # 上传任务 API
│   │   │   └── folder.py         # 文件夹验证 API
│   ├── core/
│   │   ├── services/
│   │   │   ├── upload_service.py # 上传业务逻辑
│   │   │   └── file_service.py   # 文件管理（MinIO）
│   │   ├── schemas/
│   │   │   ├── upload.py         # 上传数据模型
│   │   │   └── file.py           # 文件数据模型
│   ├── tasks/
│   │   ├── upload_task.py        # Celery 任务定义
│   │   └── retry_task.py         # 错误重试任务
core/
├── services/
│   ├── youtube/
│   │   ├── uploader.py           # YouTube 上传逻辑
│   │   ├── appium_driver.py      # Appium 控制
│   │   └── frida_monitor.py      # Frida 监控
frontend/
├── src/
│   ├── views/
│   │   ├── UploadView.vue        # 上传页面
│   │   └── FolderView.vue        # 文件夹选择页面
│   ├── api/
│   │   ├── upload.js             # 上传 API 调用
│   │   └── socket.js             # WebSocket 状态订阅
```

## 14. 示例代码

### 后端 API (backend/app/api/v1/uploads.py)

```python
from fastapi import APIRouter, Depends
from app.core.services.upload_service import UploadService
from app.core.schemas.upload import UploadCreate, UploadResponse
from app.dependencies import get_current_user

router = APIRouter()

@router.post("/uploads", response_model=UploadResponse)
async def create_upload(
    upload: UploadCreate,
    user=Depends(get_current_user),
    upload_service: UploadService = Depends()
):
    task_id = await upload_service.create_upload_task(
        source=upload.source,
        account_id=upload.account_id,
        metadata=upload.metadata,
        priority=upload.priority,
        device_id=upload.device_id,
        user_id=str(user.id)
    )
    return {"task_id": task_id, "status": "pending", "estimated_time": 3600}
```

### Celery 任务 (backend/app/tasks/upload_task.py)

```python
from celery import Celery
from app.core.services.core_client import CoreClient

app = Celery("tasks", broker="redis://redis:6379")

@app.task
def upload_video_task(file_id: str, account_id: str, metadata: dict, device_id: str):
    core_client = CoreClient(device_id=device_id)
    try:
        result = core_client.upload_video(
            file_id=file_id,
            account_id=account_id,
            metadata=metadata
        )
        return {"file_id": file_id, "youtube_id": result["youtube_id"], "status": "completed"}
    except Exception as e:
        core_client.push_to_kafka_error_queue(file_id, str(e))
        return {"file_id": file_id, "status": "failed", "error": str(e)}
```

### Core 服务 (core/services/youtube/uploader.py)

```python
import json
from services.appium.driver import AppiumDriver
from services.frida.monitor import FridaMonitor
from services.ldconsole.controller import LdConsoleController
from redis import Redis
from googleapiclient.discovery import build

class YouTubeUploader:
    def __init__(self, device_id: str):
        self.device_id = device_id
        self.appium = AppiumDriver(device_id)
        self.frida = FridaMonitor(device_id)
        self.ldconsole = LdConsoleController(device_id)
        self.redis = Redis(host="redis", port=6379)
        self.youtube_api = build("youtube", "v3", credentials=...)  # YouTube API

    def upload(self, file_id: str, account_id: str, metadata: dict) -> dict:
        try:
            self.ldconsole.push_file(file_id, "/sdcard/Download")
            self.appium.start_app("com.google.android.youtube")
            self.appium.upload_file(file_id)
            youtube_id = self.set_metadata(account_id, metadata)
            self.monitor_upload(file_id, youtube_id)
            self.ldconsole.clear_cache()
            return {"youtube_id": youtube_id}
        except Exception as e:
            self.update_status(file_id, "failed", error=str(e))
            self.push_to_kafka_error_queue(file_id, str(e))
            raise
```

### 前端 WebSocket (frontend/src/api/socket.js)

```javascript
import { io } from "socket.io-client";

const socket = io("https://backend:8000/device-socket.io", {
  transports: ["websocket"],
  secure: true,
});

export function subscribeToUploadStatus(fileId, callback) {
  socket.emit("subscribe", fileId);
  socket.on("status", (data) => {
    if (data.file_id === fileId) {
      callback(data);
    }
  });
}
```

## 15. 部署配置

### Docker Compose (docker-compose.yml)

```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "5713:5713"
    environment:
      - VITE_API_URL=https://backend:8000
    depends_on:
      - backend
  
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - MONGODB_URL=mongodb://mongodb:27017
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=consul:8500
    depends_on:
      - mongodb
      - redis
      - consul

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7.0
    ports:
      - "6379:6379"

  consul:
    image: consul:latest
    ports:
      - "8500:8500"

volumes:
  mongodb_data:
```

### Core 启动脚本 (core/scripts/setup.ps1)

```powershell
$ErrorActionPreference = "Stop"
python -m venv venv
.\venv\Scripts\activate
poetry install
$env:CONSUL_URL = "consul:8500"
$env:LDCONSOLE_CONFIG = "config/ldplayer_config.yaml"
$env:FRIDA_CONFIG = "config/frida_config.yaml"
python main.py
```

## 16. 总结

- **状态检测**：使用 Appium（UI 检测）、Frida（网络请求监控）、YouTube Data API（状态验证）和 Logcat（错误日志）
- **架构一致性**：任务和状态通过 Core 服务处理，符合 ThunderHub 多 Core 架构
- **优化措施**：
  - MinIO 分布式存储
  - Kafka 错误队列
  - 断点续传机制
  - WebSocket 消息压缩
- **部署支持**：提供完整的 Kubernetes 和 Docker Compose 部署方案
