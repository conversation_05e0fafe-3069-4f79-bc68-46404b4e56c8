#!/usr/bin/env python3
"""
手动发布任务状态测试
验证Backend是否能正确接收和处理任务状态更新
"""

import asyncio
import json
import logging
from datetime import datetime
import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_manual_publish():
    """手动发布任务状态更新"""
    try:
        # 使用正确的Redis配置
        redis_url = "redis://192.168.123.137:6379/1"
        
        # 连接Redis
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")
        
        # 要测试的任务ID
        task_id = "011379c8-c8f7-437b-8df2-abdca97275ab"
        
        # 构造任务状态数据（模拟Core服务发布的格式）
        status_data = {
            "task_id": task_id,
            "status": "failed",
            "progress": 0,
            "start_time": "2025-06-05T09:47:53.974576",
            "end_time": datetime.now().isoformat(),
            "device_usage": {
                "cpu": 0,
                "memory": 0,
                "network": "未知"
            },
            "logs": [
                {
                    "message": "手动测试：任务状态同步",
                    "level": "info",
                    "timestamp": datetime.now().isoformat()
                },
                {
                    "message": "手动测试：任务执行失败",
                    "level": "error",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "details": {
                "platform_id": "youtube",
                "account_id": "68230613013b7bb376ec174c",
                "device_id": "85",
                "content_path": "test_path",
                "created_at": "2025-06-05T09:47:14.268663"
            },
            "message": "手动测试：任务执行失败",
            "current_step": 2
        }
        
        # 发布到任务状态频道
        channel = f"task:{task_id}:status"
        status_json = json.dumps(status_data)
        
        logger.info(f"准备发布任务状态到频道: {channel}")
        logger.info(f"状态数据: {status_data}")
        
        # 发布消息
        result = await redis_client.publish(channel, status_json)
        logger.info(f"发布结果: {result}个客户端接收到消息")
        
        # 同时保存到latest键
        latest_key = f"task:{task_id}:latest"
        await redis_client.set(latest_key, status_json)
        await redis_client.expire(latest_key, 86400)  # 24小时过期
        logger.info(f"已保存最新状态到Redis键: {latest_key}")
        
        # 等待一段时间让Backend处理
        logger.info("等待5秒让Backend处理消息...")
        await asyncio.sleep(5)
        
        # 关闭连接
        await redis_client.close()
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)


async def check_subscribers():
    """检查Redis订阅者数量"""
    try:
        redis_url = "redis://192.168.123.137:6379/1"
        
        # 连接Redis
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")
        
        # 检查有多少客户端订阅了任务状态频道
        task_id = "011379c8-c8f7-437b-8df2-abdca97275ab"
        channel = f"task:{task_id}:status"
        
        # 发布一个测试消息看有多少订阅者
        test_data = {"test": True, "timestamp": datetime.now().isoformat()}
        result = await redis_client.publish(channel, json.dumps(test_data))
        logger.info(f"测试频道 {channel} 的订阅者数量: {result}")
        
        # 检查模式订阅
        pattern = "task:*:status"
        test_data = {"test": True, "pattern": pattern, "timestamp": datetime.now().isoformat()}
        
        # 发布到多个任务频道测试模式订阅
        test_channels = [
            "task:test1:status",
            "task:test2:status", 
            f"task:{task_id}:status"
        ]
        
        for test_channel in test_channels:
            result = await redis_client.publish(test_channel, json.dumps(test_data))
            logger.info(f"测试频道 {test_channel} 的订阅者数量: {result}")
        
        await redis_client.close()
        
    except Exception as e:
        logger.error(f"检查订阅者失败: {str(e)}", exc_info=True)


async def main():
    """主函数"""
    logger.info("开始手动发布任务状态测试")
    
    # 检查订阅者
    logger.info("=== 检查Redis订阅者 ===")
    await check_subscribers()
    
    # 手动发布任务状态
    logger.info("=== 手动发布任务状态 ===")
    await test_manual_publish()


if __name__ == "__main__":
    asyncio.run(main())
