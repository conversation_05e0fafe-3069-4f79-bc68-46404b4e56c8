import os
import logging
import httpx
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from bson import ObjectId

from app.core.security import get_current_user
from app.services.consul_discovery import ConsulDiscovery
from app.config.database import DatabaseConfig
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service
from urllib.parse import urlparse

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["social"],
    dependencies=[Depends(get_current_user)]
)

async def get_core_file_paths(core_service_id: str) -> Dict[str, str]:
    """获取Core服务的文件路径配置

    Args:
        core_service_id: Core服务ID

    Returns:
        Dict[str, str]: 文件路径配置
    """
    try:
        logger.info(f"获取Core服务文件路径配置: {core_service_id}")

        # 获取数据库配置
        db_config = DatabaseConfig()

        # 解析Consul URL
        consul_url = urlparse(db_config.consul_url)
        consul_host = consul_url.hostname or "localhost"
        consul_port = consul_url.port or 8500

        # 从Consul获取Core服务信息
        consul_discovery = ConsulDiscovery(
            consul_host=consul_host,
            consul_port=consul_port
        )

        # 获取Core服务实例
        services = consul_discovery.get_all_services("thunderhub-core")

        if not services or core_service_id not in services:
            logger.warning(f"未找到Core服务: {core_service_id}")
            # 返回默认配置
            return {
                "root_path": "H:\\PublishSystem",
                "platform_path_template": "{root_path}\\{platform_id}",
                "device_path_template": "{platform_path}\\{device_name}",
                "content_path_template": "{device_path}\\content"
            }

        # 获取Core服务配置
        service_info = services[core_service_id]
        host = service_info.get("host", "localhost")
        grpc_port = service_info.get("grpc_port", 50051)

        # 使用gRPC调用Core服务
        try:
            import grpc
            from google.protobuf.json_format import MessageToDict

            # 导入gRPC生成的模块
            # 注意：这里假设已经生成了gRPC代码，并且可以导入
            # 实际项目中，需要确保这些模块已经正确生成和安装
            from app.proto import file_pb2
            from app.proto import file_pb2_grpc

            # 创建gRPC通道
            logger.info(f"创建gRPC通道: {host}:{grpc_port}")
            channel = grpc.aio.insecure_channel(f"{host}:{grpc_port}")

            # 创建Stub
            logger.info("创建FileServiceStub")
            stub = file_pb2_grpc.FileServiceStub(channel)

            # 创建请求
            logger.info("创建FilePathsRequest")
            request = file_pb2.FilePathsRequest()
            logger.info(f"请求对象: {request}")

            # 调用gRPC方法
            logger.info("调用GetFilePaths方法")
            try:
                response = await stub.GetFilePaths(request)
                logger.info(f"GetFilePaths响应: {response}")
            except Exception as grpc_error:
                logger.error(f"gRPC调用失败: {str(grpc_error)}", exc_info=True)
                raise

            # 将响应转换为字典
            logger.info("将响应转换为字典")
            try:
                result = MessageToDict(response, preserving_proto_field_name=True)
                logger.info(f"转换后的字典: {result}")
            except Exception as dict_error:
                logger.error(f"转换字典失败: {str(dict_error)}", exc_info=True)
                result = {}

            # 提取文件路径相关配置
            logger.info("提取文件路径相关配置")
            root_path = result.get("file_root_path", "H:\\PublishSystem")
            platform_path_template = result.get("platform_path_template", "{root_path}\\{platform_id}")
            device_path_template = result.get("device_path_template", "{platform_path}\\{device_name}")
            content_path_template = result.get("content_path_template", "{device_path}\\content")

            logger.info(f"提取的配置: root_path={root_path}")
            logger.info(f"提取的配置: platform_path_template={platform_path_template}")
            logger.info(f"提取的配置: device_path_template={device_path_template}")
            logger.info(f"提取的配置: content_path_template={content_path_template}")

            # 关闭通道
            logger.info("关闭gRPC通道")
            await channel.close()

            # 返回结果
            result_dict = {
                "root_path": root_path,
                "platform_path_template": platform_path_template,
                "device_path_template": device_path_template,
                "content_path_template": content_path_template
            }
            logger.info(f"返回结果: {result_dict}")
            return result_dict

        except ImportError as ie:
            logger.error(f"导入gRPC模块失败: {str(ie)}")
            # 如果gRPC模块导入失败，尝试使用HTTP请求
            logger.info("尝试使用HTTP请求获取Core服务配置")

            # 获取REST端口
            rest_port = service_info.get("rest_port", 8080)

            # 构建API URL
            url = f"http://{host}:{rest_port}/api/config/paths"

            # 发送HTTP请求
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=5.0)

                if response.status_code != 200:
                    logger.warning(f"获取Core服务配置失败，状态码: {response.status_code}")
                    # 返回默认配置
                    return {
                        "root_path": "H:\\PublishSystem",
                        "platform_path_template": "{root_path}\\{platform_id}",
                        "device_path_template": "{platform_path}\\{device_name}"
                    }

                # 解析响应
                data = response.json()

                # 提取文件路径相关配置
                root_path = data.get("file_root_path", "H:\\PublishSystem")
                platform_path_template = data.get("platform_path_template", "{root_path}\\{platform_id}")
                device_path_template = data.get("device_path_template", "{platform_path}\\{device_name}")
                content_path_template = data.get("content_path_template", "{device_path}\\content")

                return {
                    "root_path": root_path,
                    "platform_path_template": platform_path_template,
                    "device_path_template": device_path_template,
                    "content_path_template": content_path_template
                }

        except Exception as e:
            logger.error(f"使用gRPC获取Core服务配置失败: {str(e)}", exc_info=True)
            # 返回默认配置
            return {
                "root_path": "H:\\PublishSystem",
                "platform_path_template": "{root_path}\\{platform_id}",
                "device_path_template": "{platform_path}\\{device_name}",
                "content_path_template": "{device_path}\\content"
            }

    except Exception as e:
        logger.error(f"获取Core服务文件路径配置失败: {str(e)}", exc_info=True)
        # 返回默认配置
        return {
            "root_path": "H:\\PublishSystem",
            "platform_path_template": "{root_path}\\{platform_id}",
            "device_path_template": "{platform_path}\\{device_name}",
            "content_path_template": "{device_path}\\content"
        }

# 定义数据模型
class PathInfo(BaseModel):
    path: str
    name: str
    is_directory: bool = True
    size: int = 0

class PathsResponse(BaseModel):
    paths: List[PathInfo]
    base_path: str

# API端点：获取平台发布路径
@router.get("/publish-paths", response_model=PathsResponse)
async def get_publish_paths(
    platform_id: str,
    core_service_id: str,
    account_id: Optional[str] = None,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取社交媒体发布路径

    - **platform_id**: 平台ID
    - **core_service_id**: Core服务ID
    - **account_id**: 可选，账号ID
    """
    try:
        logger.info(f"获取发布路径: platform_id={platform_id}, core_service_id={core_service_id}, account_id={account_id}")
        logger.info(f"请求参数: platform_id类型={type(platform_id)}, core_service_id类型={type(core_service_id)}, account_id类型={type(account_id) if account_id else None}")
        logger.info(f"数据库服务: {db_service}")

        # 获取Core服务的文件路径配置
        logger.info(f"开始获取Core服务的文件路径配置: core_service_id={core_service_id}")
        file_paths = await get_core_file_paths(core_service_id)
        logger.info(f"获取到的文件路径配置: {file_paths}")

        # 获取平台信息
        logger.info(f"开始获取平台信息: platform_id={platform_id}")
        platform = None
        try:
            platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
            logger.info(f"通过ObjectId查询平台结果: {platform}")
        except Exception as e:
            logger.warning(f"通过ObjectId查询平台失败: {str(e)}")
            try:
                platform = db_service.db.social_platforms.find_one({"id": platform_id})
                logger.info(f"通过id查询平台结果: {platform}")
            except Exception as e:
                logger.warning(f"通过id查询平台失败: {str(e)}")

        # 获取账号信息
        logger.info(f"开始获取账号信息: account_id={account_id}")
        account = None
        if account_id:
            try:
                account = db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                logger.info(f"通过ObjectId查询账号结果: {account}")
            except Exception as e:
                logger.warning(f"通过ObjectId查询账号失败: {str(e)}")
                try:
                    account = db_service.db.social_accounts.find_one({"id": account_id})
                    logger.info(f"通过id查询账号结果: {account}")
                except Exception as e:
                    logger.warning(f"通过id查询账号失败: {str(e)}")

        # 构建路径
        logger.info("开始构建路径")
        publish_base_path = file_paths.get("root_path", "H:\\PublishSystem")
        logger.info(f"发布基础路径: {publish_base_path}")

        # 获取平台名称和账号名称
        platform_name = platform.get("name", platform_id) if platform else platform_id
        account_name = account.get("username", account_id) if account else account_id

        logger.info(f"平台名称: {platform_name}, 账号名称: {account_name}")
        logger.info(f"Core服务 {core_service_id} 的发布基础路径: {publish_base_path}")

        # 构建路径
        logger.info("获取路径模板")
        platform_path_template = file_paths.get("platform_path_template", "{root_path}\\{platform_id}")
        device_path_template = file_paths.get("device_path_template", "{platform_path}\\{device_name}")
        content_path_template = file_paths.get("content_path_template", "{device_path}\\content")

        logger.info(f"平台路径模板: {platform_path_template}")
        logger.info(f"设备路径模板: {device_path_template}")
        logger.info(f"内容路径模板: {content_path_template}")

        # 替换模板变量
        logger.info("开始替换模板变量")
        try:
            platform_path = platform_path_template.format(
                root_path=publish_base_path,
                platform_id=platform_id,
                platform_name=platform_name
            )
            logger.info(f"平台路径替换成功: {platform_path}")
        except Exception as e:
            logger.error(f"平台路径替换失败: {str(e)}", exc_info=True)
            platform_path = os.path.join(publish_base_path, platform_id)
            logger.info(f"使用备用平台路径: {platform_path}")

        try:
            device_path = device_path_template.format(
                root_path=publish_base_path,
                platform_id=platform_id,
                platform_name=platform_name,
                platform_path=platform_path,
                device_name=account_name,
                account_id=account_id
            )
            logger.info(f"设备路径替换成功: {device_path}")
        except Exception as e:
            logger.error(f"设备路径替换失败: {str(e)}", exc_info=True)
            device_path = os.path.join(platform_path, account_name)
            logger.info(f"使用备用设备路径: {device_path}")

        try:
            content_path = content_path_template.format(
                root_path=publish_base_path,
                platform_id=platform_id,
                platform_name=platform_name,
                platform_path=platform_path,
                device_name=account_name,
                account_id=account_id,
                device_path=device_path
            )
            logger.info(f"内容路径替换成功: {content_path}")
        except Exception as e:
            logger.error(f"内容路径替换失败: {str(e)}", exc_info=True)
            content_path = os.path.join(device_path, "content")
            logger.info(f"使用备用内容路径: {content_path}")

        logger.info(f"最终构建的平台路径: {platform_path}")
        logger.info(f"最终构建的设备路径: {device_path}")
        logger.info(f"最终构建的内容路径: {content_path}")

        # 检查路径是否存在
        logger.info("开始检查路径是否存在")
        paths = []

        # 首先尝试内容路径
        logger.info(f"检查内容路径是否存在: {content_path}")
        content_path_exists = os.path.exists(content_path)
        logger.info(f"内容路径存在: {content_path_exists}")

        if content_path_exists:
            logger.info(f"添加内容路径到结果: {content_path}")
            paths.append(PathInfo(path=content_path, name="内容目录"))

            # 如果内容路径存在，尝试列出子目录
            try:
                logger.info(f"尝试列出内容路径子目录: {content_path}")
                items = os.listdir(content_path)
                logger.info(f"内容路径下有 {len(items)} 个项目")

                for item in items:
                    item_path = os.path.join(content_path, item)
                    is_dir = os.path.isdir(item_path)
                    logger.info(f"项目: {item}, 路径: {item_path}, 是目录: {is_dir}")

                    if is_dir:
                        logger.info(f"添加子目录到结果: {item_path}")
                        paths.append(PathInfo(path=item_path, name=item))
            except Exception as e:
                logger.warning(f"列出内容路径子目录失败: {str(e)}", exc_info=True)

        # 如果内容路径不存在，尝试设备路径
        elif os.path.exists(device_path):
            logger.info(f"添加设备路径到结果: {device_path}")
            paths.append(PathInfo(path=device_path, name="设备目录"))

            # 如果设备路径存在，尝试列出子目录
            try:
                logger.info(f"尝试列出设备路径子目录: {device_path}")
                items = os.listdir(device_path)
                logger.info(f"设备路径下有 {len(items)} 个项目")

                for item in items:
                    item_path = os.path.join(device_path, item)
                    is_dir = os.path.isdir(item_path)
                    logger.info(f"项目: {item}, 路径: {item_path}, 是目录: {is_dir}")

                    if is_dir:
                        logger.info(f"添加子目录到结果: {item_path}")
                        paths.append(PathInfo(path=item_path, name=item))
            except Exception as e:
                logger.warning(f"列出设备路径子目录失败: {str(e)}", exc_info=True)

        # 如果设备路径不存在，尝试平台路径
        elif os.path.exists(platform_path):
            logger.info(f"添加平台路径到结果: {platform_path}")
            paths.append(PathInfo(path=platform_path, name="平台目录"))

            # 如果平台路径存在，尝试列出子目录
            try:
                logger.info(f"尝试列出平台路径子目录: {platform_path}")
                items = os.listdir(platform_path)
                logger.info(f"平台路径下有 {len(items)} 个项目")

                for item in items:
                    item_path = os.path.join(platform_path, item)
                    is_dir = os.path.isdir(item_path)
                    logger.info(f"项目: {item}, 路径: {item_path}, 是目录: {is_dir}")

                    if is_dir:
                        logger.info(f"添加子目录到结果: {item_path}")
                        paths.append(PathInfo(path=item_path, name=item))
            except Exception as e:
                logger.warning(f"列出平台路径子目录失败: {str(e)}", exc_info=True)

        # 如果平台路径也不存在，尝试根路径
        elif os.path.exists(publish_base_path):
            logger.info(f"添加根路径到结果: {publish_base_path}")
            paths.append(PathInfo(path=publish_base_path, name="根目录"))

            # 如果根路径存在，尝试列出子目录
            try:
                logger.info(f"尝试列出根路径子目录: {publish_base_path}")
                items = os.listdir(publish_base_path)
                logger.info(f"根路径下有 {len(items)} 个项目")

                for item in items:
                    item_path = os.path.join(publish_base_path, item)
                    is_dir = os.path.isdir(item_path)
                    logger.info(f"项目: {item}, 路径: {item_path}, 是目录: {is_dir}")

                    if is_dir:
                        logger.info(f"添加子目录到结果: {item_path}")
                        paths.append(PathInfo(path=item_path, name=item))
            except Exception as e:
                logger.warning(f"列出根路径子目录失败: {str(e)}", exc_info=True)
        else:
            # 如果所有路径都不存在，返回空列表
            logger.warning(f"所有路径都不存在: {publish_base_path}, {platform_path}, {device_path}, {content_path}")

        # 确定基础路径
        logger.info("确定基础路径")
        base_path = content_path
        if not os.path.exists(content_path):
            if os.path.exists(device_path):
                base_path = device_path
                logger.info(f"使用设备路径作为基础路径: {base_path}")
            elif os.path.exists(platform_path):
                base_path = platform_path
                logger.info(f"使用平台路径作为基础路径: {base_path}")
            else:
                base_path = publish_base_path
                logger.info(f"使用根路径作为基础路径: {base_path}")
        else:
            logger.info(f"使用内容路径作为基础路径: {base_path}")

        # 构建响应
        logger.info(f"构建响应: paths={paths}, base_path={base_path}")
        response = PathsResponse(
            paths=paths,
            base_path=base_path
        )
        logger.info(f"返回响应: {response}")
        return response

    except Exception as e:
        logger.error(f"获取发布路径失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取发布路径失败: {str(e)}"
        )
