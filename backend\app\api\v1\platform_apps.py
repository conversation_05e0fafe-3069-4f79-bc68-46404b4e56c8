"""
平台应用API (v1版本)
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from datetime import datetime

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["social"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class PlatformApp(BaseModel):
    id: Optional[str] = None
    platform_id: str
    name: str
    type: str
    status: str = "active"
    version: Optional[str] = None
    app_info: Optional[Dict[str, Any]] = None
    automation: Optional[Dict[str, Any]] = None

# API端点：获取平台应用列表
@router.get("/platform_apps", response_model=List[Dict[str, Any]])
async def get_platform_apps(
    platform_id: Optional[str] = None,
    type: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取平台应用列表
    
    - **platform_id**: 可选，平台ID筛选
    - **type**: 可选，应用类型筛选
    - **status**: 可选，应用状态筛选
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    try:
        logger.info(f"获取平台应用列表: platform_id={platform_id}, type={type}, status={status}")
        
        # 构建查询条件
        query = {}
        if platform_id:
            query["platform_id"] = platform_id
        if type:
            query["type"] = type
        if status:
            query["status"] = status
            
        # 查询平台应用
        apps = list(db_service.db.platform_apps.find(query).skip(skip).limit(limit))
        
        # 格式化返回数据
        formatted_apps = []
        for app in apps:
            # 确保ID字段一致
            if '_id' in app:
                app['id'] = str(app['_id'])
                del app['_id']
                
            formatted_apps.append(app)
            
        logger.info(f"找到 {len(formatted_apps)} 个平台应用")
        return formatted_apps
        
    except Exception as e:
        logger.error(f"获取平台应用列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取平台应用列表失败: {str(e)}"
        )

# API端点：获取单个平台应用
@router.get("/platform_apps/{app_id}", response_model=Dict[str, Any])
async def get_platform_app(
    app_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取单个平台应用
    
    - **app_id**: 应用ID
    """
    try:
        logger.info(f"获取平台应用: {app_id}")
        
        # 查询平台应用
        app = db_service.db.platform_apps.find_one({"id": app_id})
        
        if not app:
            raise HTTPException(status_code=404, detail="平台应用不存在")
            
        # 格式化返回数据
        if '_id' in app:
            app['id'] = str(app['_id'])
            del app['_id']
            
        logger.info(f"找到平台应用: {app}")
        return app
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取平台应用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取平台应用失败: {str(e)}"
        )

# API端点：创建平台应用
@router.post("/platform_apps", response_model=Dict[str, Any])
async def create_platform_app(
    app: PlatformApp,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    创建平台应用
    
    - **app**: 应用信息
    """
    try:
        logger.info(f"创建平台应用: {app.model_dump()}")
        
        # 检查平台是否存在
        platform = db_service.db.social_platforms.find_one({"id": app.platform_id})
        if not platform:
            raise HTTPException(status_code=400, detail="平台不存在")
            
        # 检查应用ID是否已存在
        if app.id:
            existing = db_service.db.platform_apps.find_one({"id": app.id})
            if existing:
                raise HTTPException(status_code=400, detail="应用ID已存在")
        
        # 创建应用
        app_data = app.model_dump()
        
        # 添加创建和更新时间
        now = datetime.now()
        app_data["created_at"] = now
        app_data["updated_at"] = now
        
        result = db_service.db.platform_apps.insert_one(app_data)
        
        # 获取创建的应用
        created_app = db_service.db.platform_apps.find_one({"_id": result.inserted_id})
        
        # 格式化返回数据
        if '_id' in created_app:
            created_app['id'] = str(created_app['_id'])
            del created_app['_id']
            
        logger.info(f"平台应用创建成功: {created_app}")
        return created_app
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建平台应用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"创建平台应用失败: {str(e)}"
        )

# API端点：更新平台应用
@router.put("/platform_apps/{app_id}", response_model=Dict[str, Any])
async def update_platform_app(
    app_id: str,
    app: PlatformApp,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    更新平台应用
    
    - **app_id**: 应用ID
    - **app**: 更新的应用信息
    """
    try:
        logger.info(f"更新平台应用 {app_id}: {app.model_dump()}")
        
        # 检查应用是否存在
        existing = db_service.db.platform_apps.find_one({"id": app_id})
        if not existing:
            raise HTTPException(status_code=404, detail="平台应用不存在")
            
        # 更新应用
        update_data = app.model_dump(exclude={"id"}, exclude_unset=True)
        
        # 更新时间
        update_data["updated_at"] = datetime.now()
        
        result = db_service.db.platform_apps.update_one(
            {"id": app_id},
            {"$set": update_data}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="更新平台应用失败")
            
        # 获取更新后的应用
        updated_app = db_service.db.platform_apps.find_one({"id": app_id})
        
        # 格式化返回数据
        if '_id' in updated_app:
            updated_app['id'] = str(updated_app['_id'])
            del updated_app['_id']
            
        logger.info(f"平台应用更新成功: {updated_app}")
        return updated_app
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新平台应用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"更新平台应用失败: {str(e)}"
        )

# API端点：删除平台应用
@router.delete("/platform_apps/{app_id}", response_model=Dict[str, bool])
async def delete_platform_app(
    app_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    删除平台应用
    
    - **app_id**: 应用ID
    """
    try:
        logger.info(f"删除平台应用: {app_id}")
        
        # 检查应用是否存在
        existing = db_service.db.platform_apps.find_one({"id": app_id})
        if not existing:
            raise HTTPException(status_code=404, detail="平台应用不存在")
            
        # 删除应用
        result = db_service.db.platform_apps.delete_one({"id": app_id})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=500, detail="删除平台应用失败")
            
        logger.info(f"平台应用删除成功: {app_id}")
        return {"deleted": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除平台应用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"删除平台应用失败: {str(e)}"
        )
