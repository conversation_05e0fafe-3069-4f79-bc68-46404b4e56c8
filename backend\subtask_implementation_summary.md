# 子任务拆分功能实现总结

## 🎯 功能描述

实现创建任务时如果目录下有多个视频，拆分为每个视频一个子任务执行的功能。

## 🛠️ 实现方案

### 1. 后端API修改

**修改文件**: `backend/app/api/v1/youtube_upload.py`

**核心逻辑**:
```python
# 检查是否需要拆分子任务
if task.selectedFiles and len(task.selectedFiles) > 1:
    # 创建主任务
    main_task_data = task_data.copy()
    main_task_data["task_type"] = "main"
    main_task_data["total_subtasks"] = len(task.selectedFiles)
    main_task_data["completed_subtasks"] = 0
    
    # 为每个视频文件创建子任务
    for i, video_file in enumerate(task.selectedFiles):
        subtask_data = task_data.copy()
        subtask_data["task_type"] = "subtask"
        subtask_data["parent_task_id"] = main_task_id
        subtask_data["subtask_index"] = i + 1
        subtask_data["selected_files"] = [video_file]
        subtask_data["video_file"] = video_file
        
        # 为子任务生成独特的标题
        subtask_data["metadata"]["title"] = f"{base_title} - 第{i+1}部分"
```

### 2. Core服务修改

**修改文件**: `core/src/services/task_executor.py`

**主要功能**:
1. **任务类型检测**: 在`execute_task`方法中检查任务类型
2. **主任务执行**: 新增`execute_main_task`方法管理子任务执行
3. **子任务处理**: 为子任务添加特殊的日志标识

**核心逻辑**:
```python
# 检查任务类型
task_type = task.get("task_type", "single")

# 如果是主任务，需要执行子任务
if task_type == "main":
    await self.execute_main_task(task_id, task)
    return

# 如果是子任务，正常执行
if task_type == "subtask":
    self.add_task_log(task_id, f"开始执行子任务 {task.get('subtask_index', 1)}", "info")
```

## 📊 数据库结构

### 任务类型分类

1. **单个任务** (`task_type: "single"`)
   - 传统的单个视频上传任务
   - 直接执行，无子任务

2. **主任务** (`task_type: "main"`)
   - 管理多个子任务的执行
   - 包含`total_subtasks`和`completed_subtasks`字段
   - 不直接执行具体操作，只管理子任务

3. **子任务** (`task_type: "subtask"`)
   - 实际执行视频上传的任务
   - 包含`parent_task_id`和`subtask_index`字段
   - 每个子任务处理一个视频文件

### 数据库字段

**主任务字段**:
```json
{
  "task_id": "main-task-uuid",
  "task_type": "main",
  "total_subtasks": 3,
  "completed_subtasks": 0,
  "selected_files": ["video1.mp4", "video2.mp4", "video3.mp4"],
  "status": "pending"
}
```

**子任务字段**:
```json
{
  "task_id": "subtask-uuid",
  "task_type": "subtask",
  "parent_task_id": "main-task-uuid",
  "subtask_index": 1,
  "selected_files": ["video1.mp4"],
  "video_file": "video1.mp4",
  "metadata": {
    "title": "我的视频 - 第1部分"
  }
}
```

## 🎯 执行流程

### 1. 任务创建流程

```mermaid
graph TD
    A[用户选择多个视频] --> B{检查文件数量}
    B -->|单个文件| C[创建单个任务]
    B -->|多个文件| D[创建主任务]
    D --> E[为每个视频创建子任务]
    E --> F[返回主任务ID和子任务列表]
```

### 2. 任务执行流程

```mermaid
graph TD
    A[开始执行任务] --> B{检查任务类型}
    B -->|single| C[直接执行单个任务]
    B -->|main| D[执行主任务逻辑]
    B -->|subtask| E[执行子任务逻辑]
    D --> F[逐个执行子任务]
    F --> G[更新主任务进度]
    G --> H[主任务完成]
```

## 🔧 API响应格式

### 单个任务响应
```json
{
  "task_id": "single-task-uuid",
  "status": "pending",
  "task_type": "single",
  "estimated_time": 3600
}
```

### 多任务响应
```json
{
  "task_id": "main-task-uuid",
  "status": "pending",
  "task_type": "main",
  "subtask_ids": ["subtask1-uuid", "subtask2-uuid", "subtask3-uuid"],
  "total_subtasks": 3,
  "estimated_time": 10800
}
```

## 📈 进度管理

### 主任务进度计算
```python
# 主任务进度 = (已完成子任务数 + 当前子任务进度) / 总子任务数 * 100
overall_progress = int((completed_subtasks + current_progress / 100) / total_subtasks * 100)
```

### 状态同步
- 子任务完成时更新主任务的`completed_subtasks`
- 主任务进度实时反映所有子任务的整体进度
- 主任务状态基于所有子任务的执行结果

## 🎯 用户体验优化

### 1. 标题自动生成
- 基础标题: "我的视频"
- 子任务标题: "我的视频 - 第1部分", "我的视频 - 第2部分"

### 2. 进度显示
- 主任务显示整体进度
- 子任务显示当前执行的具体进度
- 实时更新执行状态

### 3. 日志管理
- 主任务日志: 显示子任务管理信息
- 子任务日志: 显示具体的执行过程
- 层次化的日志结构

## 🚀 扩展功能

### 1. 并行执行
- 当前实现是串行执行子任务
- 未来可以支持并行执行多个子任务
- 需要考虑设备资源限制

### 2. 失败重试
- 子任务失败时可以单独重试
- 主任务可以跳过失败的子任务继续执行
- 提供灵活的错误处理策略

### 3. 优先级管理
- 支持子任务优先级设置
- 重要视频优先上传
- 动态调整执行顺序

## 🎉 实现效果

### 修复前的问题
- ❌ **单一任务处理**: 多个视频只能创建一个任务
- ❌ **进度不明确**: 无法知道具体哪个视频在处理
- ❌ **失败影响全部**: 一个视频失败导致整个任务失败

### 修复后的改进
- ✅ **智能任务拆分**: 多个视频自动拆分为独立子任务
- ✅ **精确进度跟踪**: 可以看到每个视频的处理进度
- ✅ **独立错误处理**: 单个视频失败不影响其他视频
- ✅ **灵活任务管理**: 支持暂停、取消特定子任务

## 📋 测试场景

### 1. 单个视频
- 创建任务时选择1个视频文件
- 应该创建`task_type: "single"`的任务
- 正常执行上传流程

### 2. 多个视频
- 创建任务时选择3个视频文件
- 应该创建1个主任务 + 3个子任务
- 主任务管理子任务的执行

### 3. 混合场景
- 测试不同数量的视频文件
- 验证进度计算的准确性
- 确认错误处理的正确性

现在系统支持智能的任务拆分功能，为用户提供更好的多视频上传体验！🚀
