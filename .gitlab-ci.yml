stages:
  - build
  - sync_wiki
  - deploy_dev
  - backup_data

variables:
  CI_REGISTRY: ***************:5050
  CI_REGISTRY_IMAGE: ***************:5050/docker/ci-images/longer-thunderhub
  DEV_SERVER: ***************
  HTTP_PROXY: http://*************:10810
  NO_PROXY: *************/24,localhost,127.0.0.1,docker
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  DATA_STORAGE_DIR: /home/<USER>/thunderhub/data
  MONGO_IMAGE: $CI_REGISTRY/docker/ci-images/mongo:5.0
  REDIS_IMAGE: $CI_REGISTRY/docker/ci-images/redis:7.0
  CONSUL_IMAGE: $CI_REGISTRY/docker/ci-images/consul:1.20
  TRAEFIK_IMAGE: $CI_REGISTRY/docker/ci-images/traefik:v2.10
# 通用 Docker 配置模板
.docker_setup:
  image: $CI_REGISTRY/docker/ci-images/docker:20.10-with-gettext
  services:
    - name: $CI_REGISTRY/docker/ci-images/docker:20.10-dind
      alias: docker
      command: ["--tls=false", "--insecure-registry=***************:5050"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:  
    - mkdir -p /etc/docker
    - |
      cat > /etc/docker/daemon.json <<EOF
      {
        "insecure-registries":["$CI_REGISTRY"],
        "proxies": {
          "http-proxy":"$HTTP_PROXY",
          "https-proxy":"$HTTP_PROXY",
          "no-proxy":"$NO_PROXY"
        }
      }
      EOF
    - export HTTP_PROXY="$HTTP_PROXY"
    - export HTTPS_PROXY="$HTTP_PROXY"
    - export NO_PROXY="$NO_PROXY"
    - for i in {1..30}; do docker info && break; sleep 2; done
    - docker info --format '{{.ServerVersion}}' || echo "Docker daemon not running"
    - echo "$CI_IMAGES_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY

build_wiki_image:
  extends: .docker_setup
  stage: build
  script:
    - docker build -t $CI_REGISTRY_IMAGE-ci-wiki-image:3.21 . || { echo "Docker build failed"; exit 1; }
    - docker push $CI_REGISTRY_IMAGE-ci-wiki-image:3.21 || { echo "Docker push failed"; exit 1; }
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      changes:
        - Dockerfile
  timeout: 1 hour

sync_wiki:
  stage: sync_wiki
  image: $CI_REGISTRY_IMAGE-ci-wiki-image:3.21
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    - git clone "http://oauth2:$WIKI_ACCESS_TOKEN@***************/${CI_PROJECT_PATH}.wiki.git" wiki || { echo "Git clone failed"; exit 1; }
    - cd wiki
    - git checkout main || git checkout -b main
    - rsync -av --delete --exclude '.git' ../docs/ .
    - git add .
    - if git diff --staged --quiet; then echo "No changes to commit"; else git commit -m "Sync docs to Wiki from commit $CI_COMMIT_SHA" && git push origin main; fi
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      changes:
        - docs/*
  timeout: 30 minutes
  
build_frontend_dependency_image:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build -f Dockerfile.dependencies -t $CI_REGISTRY_IMAGE-frontend-dependencies:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend-dependencies:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/package.json
        - frontend/package-lock.json
        - frontend/Dockerfile.dependencies
  timeout: 30 minutes

build_frontend_build_image:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG --build-arg VITE_API_URL=http://$DEV_SERVER:8000 --build-arg VITE_WS_URL=ws://$DEV_SERVER:8000 -f Dockerfile.builder -t $CI_REGISTRY_IMAGE-frontend-build:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend-build:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/src/**/*
        - frontend/public/**/*
        - frontend/*.config.*  # 包含 vite.config.js, webpack.config.js 等
        - frontend/Dockerfile.builder
        - frontend/*.html
        - frontend/tsconfig.*
      when: always
  dependencies:
    - build_frontend_dependency_image
  timeout: 20 minutes

build_frontend_dev:
  extends: .docker_setup
  stage: build
  script:
    - cd frontend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -t $CI_REGISTRY_IMAGE-frontend:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-frontend:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/nginx.conf
        - frontend/Dockerfile
      when: always
  dependencies:
    - build_frontend_build_image
  timeout: 10 minutes

build_backend_base_image:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build -f Dockerfile.base -t $CI_REGISTRY_IMAGE-python-base:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-python-base:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main"'
      changes:
        - backend/Dockerfile.base
  timeout: 2 hours

build_backend_dependency_image:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -f Dockerfile.dependencies -t $CI_REGISTRY_IMAGE-python-dependencies:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-python-dependencies:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main"'
      changes:
        - backend/Dockerfile.dependencies
        - backend/requirements.txt
  dependencies:
    - build_backend_base_image
  timeout: 30 minutes

build_backend_dev:
  extends: .docker_setup
  stage: build
  script:
    - cd backend
    - docker build --build-arg IMAGE_TAG=$IMAGE_TAG -t $CI_REGISTRY_IMAGE-backend:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-backend:$IMAGE_TAG
  rules:
     - if: '$CI_COMMIT_BRANCH == "dev"'
       changes:
        - backend/app/**/*  # 假设代码文件在 backend/src/ 下
        - backend/scripts/**/*  # 假设其他代码在 backend/scripts/ 下
        - backend/Dockerfile  # 显式包含 Dockerfile
        - backend/requirements.txt  # 依赖
        - backend/run_server.py  # 依赖
        - backend/cors_config.yaml  # 跨域配置
        # 其他特定子目录或文件模式
       when: always
  dependencies:
    - build_backend_dependency_image
  timeout: 30 minutes
  
# 部署通用配置模板
.deploy_setup:
  extends: .docker_setup
  before_script:
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "docker --version && docker-compose --version" || { echo "Docker or Docker Compose not installed on $DEV_SERVER"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "mkdir -p $DATA_STORAGE_DIR/mongodb $DATA_STORAGE_DIR/redis $DATA_STORAGE_DIR/consul $DATA_STORAGE_DIR/traefik"
    - echo "docker_setup done"

# 新增：部署 Traefik 服务
deploy_dev_traefik:
  extends: .deploy_setup
  stage: deploy_dev
  script:
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        docker image inspect $TRAEFIK_IMAGE >/dev/null 2>&1 || docker pull $TRAEFIK_IMAGE" || { echo "Failed to pull Traefik image on $DEV_SERVER"; exit 1; }
    - scp -v api/traefik.yml longer@$DEV_SERVER:$DATA_STORAGE_DIR/traefik/traefik.yml || { echo "SCP traefik.yml failed"; exit 1; }
    - scp -v api/dynamic.yml longer@$DEV_SERVER:$DATA_STORAGE_DIR/traefik/dynamic.yml || { echo "SCP dynamic.yml failed"; exit 1; }  
    - envsubst < docker-compose.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/thunderhub/docker-compose.yml || { echo "SCP docker-compose.yml failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/thunderhub &&
        docker-compose pull traefik &&
        docker-compose up -d traefik &&
        docker-compose ps --services &&
        echo 'Traefik deployment completed' || { echo 'Traefik deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - api/traefik.yml
        - api/dynamic.yml
      when: always
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - docker-compose.yml
      when: manual
      allow_failure: true
  timeout: 20 minutes

# 部署前端服务
deploy_dev_frontend:
  extends: .deploy_setup
  stage: deploy_dev
  script:
    - envsubst < docker-compose.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/thunderhub/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/thunderhub &&
        docker-compose pull frontend &&
        docker-compose up -d frontend &&
        docker-compose ps --services &&
        echo 'Frontend deployment completed' || { echo 'Frontend deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - frontend/**/*
      when: always
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - docker-compose.yml
      when: manual
      allow_failure: true
  dependencies:
    - build_frontend_dev
  timeout: 20 minutes

# 部署后端服务
deploy_dev_backend:
  extends: .deploy_setup
  stage: deploy_dev
  script:
    - envsubst < docker-compose.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/thunderhub/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/thunderhub &&
        docker-compose pull backend &&
        docker-compose up -d backend &&
        docker-compose ps --services &&
        echo 'Backend deployment completed' || { echo 'Backend deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - backend/**/*        
      when: always
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - docker-compose.yml
      when: manual
      allow_failure: true
  dependencies:
    - build_backend_dev
  timeout: 20 minutes

# 部署数据库服务（MongoDB、Redis、Consul）
deploy_dev_databases:
  extends: .deploy_setup
  stage: deploy_dev
  before_script:
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        docker image inspect $MONGO_IMAGE >/dev/null 2>&1 || docker pull $MONGO_IMAGE &&
        docker image inspect $REDIS_IMAGE >/dev/null 2>&1 || docker pull $REDIS_IMAGE &&
        docker image inspect $CONSUL_IMAGE >/dev/null 2>&1 || docker pull $CONSUL_IMAGE" || { echo "Failed to pull database images on $DEV_SERVER"; exit 1; }
  script:
    - envsubst < docker-compose.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/thunderhub/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/thunderhub &&
        docker-compose pull mongodb redis consul &&
        docker-compose up -d mongodb redis consul &&
        docker-compose ps --services &&
        echo 'Databases deployment completed' || { echo 'Databases deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - docker-compose.yml
      when: manual
      allow_failure: true
  timeout: 25 minutes

backup_data:
  stage: backup_data
  image: $CI_REGISTRY/docker/ci-images/docker:20.10
  before_script:
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
  script:
    - mkdir -p data_backup/mongodb data_backup/redis data_backup/consul
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/mongodb/* data_backup/mongodb/ || echo "No MongoDB data to backup"
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/redis/* data_backup/redis/ || echo "No Redis data to backup"
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/consul/* data_backup/consul/ || echo "No Consul data to backup"
  artifacts:
    paths:
      - data_backup/
    expire_in: 1 month
    when: always
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: manual
      allow_failure: true
  timeout: 30 minutes

appium:
  host: localhost
  port: 4723  # 每个Core服务使用不同的端口
  log_level: info
