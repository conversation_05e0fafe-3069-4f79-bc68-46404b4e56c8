"""
社交媒体平台API (v1版本)
"""

import logging
import os
import shutil
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, UploadFile, File
from pydantic import BaseModel
from datetime import datetime
from bson import ObjectId

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social",
    tags=["social"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class SocialPlatform(BaseModel):
    id: Optional[str] = None
    name: str
    icon: Optional[str] = None
    website: Optional[str] = None
    status: str = "active"
    features: Optional[List[str]] = None

class PlatformApp(BaseModel):
    id: Optional[str] = None
    platform_id: str
    name: str
    type: str
    status: str = "active"
    version: Optional[str] = None
    app_info: Optional[Dict[str, Any]] = None
    automation: Optional[Dict[str, Any]] = None

# API端点：获取平台列表
@router.get("/platforms", response_model=List[Dict[str, Any]])
async def get_platforms(
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取社交媒体平台列表

    - **status**: 可选，平台状态筛选
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    try:
        logger.info(f"获取社交平台列表: status={status}")

        # 构建查询条件
        query = {}
        if status:
            query["status"] = status

        # 检查集合是否存在
        collections = db_service.db.list_collection_names()
        logger.info(f"数据库集合列表: {collections}")

        # 查询平台
        if 'social_platforms' in collections:
            platforms = list(db_service.db.social_platforms.find(query).skip(skip).limit(limit))
            logger.info(f"从social_platforms集合中找到 {len(platforms)} 个平台")
        else:
            # 尝试其他可能的集合名称
            alternative_collections = ['platforms', 'social_platform']
            platforms = []
            for coll in alternative_collections:
                if coll in collections:
                    platforms = list(db_service.db[coll].find(query).skip(skip).limit(limit))
                    logger.info(f"从{coll}集合中找到 {len(platforms)} 个平台")
                    break

        # 格式化返回数据
        formatted_platforms = []
        for platform in platforms:
            # 确保ID字段一致
            if '_id' in platform:
                platform['id'] = str(platform['_id'])
                del platform['_id']

            formatted_platforms.append(platform)

        logger.info(f"最终找到 {len(formatted_platforms)} 个平台")
        return formatted_platforms

    except Exception as e:
        logger.error(f"获取社交平台列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取社交平台列表失败: {str(e)}"
        )

# API端点：获取单个平台
@router.get("/platforms/{platform_id}", response_model=Dict[str, Any])
async def get_platform(
    platform_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取单个社交媒体平台

    - **platform_id**: 平台ID或ObjectId
    """
    try:
        logger.info(f"获取社交平台: {platform_id}")

        # 尝试将platform_id转换为ObjectId
        try:
            object_id = ObjectId(platform_id)
            is_object_id = True
        except:
            is_object_id = False

        # 构建查询条件
        query = {"_id": object_id} if is_object_id else {"id": platform_id}

        # 查询平台
        platform = db_service.db.social_platforms.find_one(query)

        if not platform:
            # 如果找不到，尝试另一种查询方式
            if is_object_id:
                platform = db_service.db.social_platforms.find_one({"id": platform_id})
            else:
                try:
                    platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                except:
                    pass

            if not platform:
                raise HTTPException(status_code=404, detail=f"平台不存在: {platform_id}")

        # 格式化返回数据
        if '_id' in platform:
            platform['id'] = str(platform['_id'])
            del platform['_id']

        logger.info(f"找到平台: {platform}")
        return platform

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取社交平台失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取社交平台失败: {str(e)}"
        )

# API端点：创建平台
@router.post("/platforms", response_model=Dict[str, Any])
async def create_platform(
    platform: SocialPlatform,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    创建社交媒体平台

    - **platform**: 平台信息
    """
    try:
        logger.info(f"创建社交平台: {platform.model_dump()}")

        # 检查平台ID是否已存在
        if platform.id:
            existing = db_service.db.social_platforms.find_one({"id": platform.id})
            if existing:
                raise HTTPException(status_code=400, detail="平台ID已存在")

        # 创建平台
        platform_data = platform.model_dump()

        # 添加创建和更新时间
        now = datetime.now()
        platform_data["created_at"] = now
        platform_data["updated_at"] = now

        result = db_service.db.social_platforms.insert_one(platform_data)

        # 获取创建的平台
        created_platform = db_service.db.social_platforms.find_one({"_id": result.inserted_id})

        # 格式化返回数据
        if '_id' in created_platform:
            created_platform['id'] = str(created_platform['_id'])
            del created_platform['_id']

        logger.info(f"平台创建成功: {created_platform}")
        return created_platform

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建社交平台失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"创建社交平台失败: {str(e)}"
        )

# API端点：更新平台
@router.put("/platforms/{platform_id}", response_model=Dict[str, Any])
async def update_platform(
    platform_id: str,
    platform_data: Dict[str, Any],
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    更新社交媒体平台

    - **platform_id**: 平台ID或ObjectId
    - **platform_data**: 更新的平台信息
    """
    try:
        # 打印详细的更新信息，帮助调试
        print(f"更新平台请求: platform_id={platform_id}, platform_data={platform_data}")
        logger.info(f"更新社交平台 {platform_id}: {platform_data}")

        # 尝试将platform_id转换为ObjectId
        try:
            object_id = ObjectId(platform_id)
            is_object_id = True
        except:
            is_object_id = False

        # 构建查询条件
        query = {"_id": object_id} if is_object_id else {"id": platform_id}
        print(f"查询条件: {query}")

        # 检查平台是否存在
        existing = db_service.db.social_platforms.find_one(query)
        if not existing:
            # 如果找不到，尝试另一种查询方式
            if is_object_id:
                existing = db_service.db.social_platforms.find_one({"id": platform_id})
                if existing:
                    query = {"id": platform_id}
            else:
                try:
                    existing = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                    if existing:
                        query = {"_id": ObjectId(platform_id)}
                except:
                    pass

            if not existing:
                raise HTTPException(status_code=404, detail=f"平台不存在: {platform_id}")

        print(f"找到平台: {existing}")

        # 更新平台
        # 从更新数据中移除id和_id字段
        if "id" in platform_data:
            del platform_data["id"]
        if "_id" in platform_data:
            del platform_data["_id"]

        # 确保图标路径是完整的URL路径
        if "icon" in platform_data and platform_data["icon"]:
            icon_path = platform_data["icon"]
            if not icon_path.startswith("/") and not icon_path.startswith("http"):
                platform_data["icon"] = f"/icons/{icon_path}"
                print(f"已将图标路径转换为完整URL路径: {platform_data['icon']}")

        # 更新时间
        platform_data["updated_at"] = datetime.now()

        print(f"更新数据: {platform_data}")

        # 执行更新
        result = db_service.db.social_platforms.update_one(
            query,
            {"$set": platform_data}
        )

        print(f"更新结果: modified_count={result.modified_count}, matched_count={result.matched_count}")

        if result.modified_count == 0 and result.matched_count == 0:
            raise HTTPException(status_code=500, detail="更新平台失败")

        # 获取更新后的平台
        updated_platform = db_service.db.social_platforms.find_one(query)
        print(f"更新后的平台: {updated_platform}")

        # 格式化返回数据
        if '_id' in updated_platform:
            updated_platform['id'] = str(updated_platform['_id'])
            del updated_platform['_id']

        logger.info(f"平台更新成功: {updated_platform}")
        return updated_platform

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新社交平台失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"更新社交平台失败: {str(e)}"
        )

# API端点：删除平台
@router.delete("/platforms/{platform_id}", response_model=Dict[str, bool])
async def delete_platform(
    platform_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    删除社交媒体平台

    - **platform_id**: 平台ID或ObjectId
    """
    try:
        logger.info(f"删除社交平台: {platform_id}")

        # 尝试将platform_id转换为ObjectId
        try:
            object_id = ObjectId(platform_id)
            is_object_id = True
        except:
            is_object_id = False

        # 构建查询条件
        query = {"_id": object_id} if is_object_id else {"id": platform_id}

        # 检查平台是否存在
        existing = db_service.db.social_platforms.find_one(query)
        if not existing:
            # 如果找不到，尝试另一种查询方式
            if is_object_id:
                existing = db_service.db.social_platforms.find_one({"id": platform_id})
                if existing:
                    query = {"id": platform_id}
            else:
                try:
                    existing = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                    if existing:
                        query = {"_id": ObjectId(platform_id)}
                except:
                    pass

            if not existing:
                raise HTTPException(status_code=404, detail=f"平台不存在: {platform_id}")

        # 删除平台
        result = db_service.db.social_platforms.delete_one(query)

        if result.deleted_count == 0:
            raise HTTPException(status_code=500, detail="删除平台失败")

        logger.info(f"平台删除成功: {platform_id}")
        return {"deleted": True}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除社交平台失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"删除社交平台失败: {str(e)}"
        )

# API端点：上传平台图标
@router.post("/platforms/upload-icon", response_model=Dict[str, str])
async def upload_platform_icon(
    file: UploadFile = File(...),
    platform_id: Optional[str] = None,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    # 打印所有请求参数，帮助调试
    print(f"上传图标请求参数: platform_id={platform_id}")
    """
    上传平台图标

    - **file**: 图标文件
    - **platform_id**: 平台ID（可选）
    """
    try:
        logger.info(f"上传平台图标: {file.filename}, 平台ID: {platform_id}")

        # 检查文件类型
        allowed_types = ["image/jpeg", "image/png", "image/gif", "image/svg+xml"]
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="只支持JPEG、PNG、GIF和SVG格式的图片")

        # 创建图标存储目录
        icons_dir = os.path.join("static", "icons")
        os.makedirs(icons_dir, exist_ok=True)

        # 生成文件名
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ".png"

        # 如果提供了平台ID，使用平台ID作为文件名前缀
        platform = None
        if platform_id:
            # 检查平台是否存在
            try:
                # 尝试将platform_id转换为ObjectId
                try:
                    object_id = ObjectId(platform_id)
                    is_object_id = True
                except:
                    is_object_id = False

                # 构建查询条件
                query = {"_id": object_id} if is_object_id else {"id": platform_id}

                # 查询平台
                platform = db_service.db.social_platforms.find_one(query)

                if not platform:
                    # 如果找不到，尝试另一种查询方式
                    if is_object_id:
                        platform = db_service.db.social_platforms.find_one({"id": platform_id})
                    else:
                        try:
                            platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                        except:
                            pass

                if platform:
                    # 使用平台ID作为文件名前缀
                    platform_id_safe = platform.get("id", "").replace(" ", "_").lower()
                    if not platform_id_safe:
                        platform_id_safe = str(platform.get("_id", "")).replace(" ", "_").lower()

                    filename = f"{platform_id_safe}_icon{file_extension}"
                else:
                    # 如果平台不存在，使用时间戳
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    filename = f"platform_icon_{timestamp}{file_extension}"
            except Exception as e:
                logger.error(f"查询平台失败: {str(e)}")
                # 如果查询失败，使用时间戳
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                filename = f"platform_icon_{timestamp}{file_extension}"
        else:
            # 如果没有提供平台ID，使用时间戳
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"platform_icon_{timestamp}{file_extension}"

        file_path = os.path.join(icons_dir, filename)

        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 返回标准的URL路径（不包含域名）
        url_path = f"/icons/{filename}"
        logger.info(f"平台图标上传成功: {url_path}")

        # 如果提供了平台ID并且找到了平台，直接更新平台的图标路径
        if platform_id and platform:
            try:
                # 构建查询条件
                query = {"_id": platform["_id"]} if "_id" in platform else {"id": platform["id"]}

                # 打印更新信息
                print(f"更新平台图标路径: 平台ID={platform_id}, 查询条件={query}, 新图标路径={url_path}")

                # 更新平台图标路径
                update_result = db_service.db.social_platforms.update_one(
                    query,
                    {"$set": {"icon": url_path, "updated_at": datetime.now()}}
                )

                print(f"更新结果: modified_count={update_result.modified_count}, matched_count={update_result.matched_count}")

                if update_result.modified_count > 0:
                    logger.info(f"已自动更新平台 {platform_id} 的图标路径: {url_path}")
                    print(f"已自动更新平台 {platform_id} 的图标路径: {url_path}")
                else:
                    logger.warning(f"未能更新平台 {platform_id} 的图标路径")
                    print(f"未能更新平台 {platform_id} 的图标路径")

                # 无论是否更新成功，都再次查询平台，确认图标路径
                updated_platform = db_service.db.social_platforms.find_one(query)
                if updated_platform:
                    print(f"更新后的平台数据: id={updated_platform.get('id')}, icon={updated_platform.get('icon')}")
                else:
                    print(f"无法查询到更新后的平台数据")
            except Exception as e:
                logger.error(f"更新平台图标路径失败: {str(e)}")
                print(f"更新平台图标路径失败: {str(e)}")

        return {"path": url_path}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传平台图标失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"上传平台图标失败: {str(e)}"
        )
