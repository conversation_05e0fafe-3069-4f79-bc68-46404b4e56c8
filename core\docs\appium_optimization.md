# Appium UiAutomator2 优化配置

## 🔧 推荐的Appium配置

### 基础配置
```python
desired_caps = {
    "platformName": "Android",
    "deviceName": "your_device",
    "appPackage": "com.google.android.youtube",
    "appActivity": "com.google.android.apps.youtube.app.WatchWhileActivity",
    
    # UiAutomator2 稳定性配置
    "automationName": "UiAutomator2",
    "uiautomator2ServerLaunchTimeout": 60000,
    "uiautomator2ServerInstallTimeout": 60000,
    "uiautomator2ServerReadTimeout": 120000,
    
    # 性能优化
    "newCommandTimeout": 300,  # 5分钟超时
    "implicitWaitTimeout": 10000,
    "waitForIdleTimeout": 1000,
    
    # 稳定性增强
    "skipServerInstallation": False,
    "skipDeviceInitialization": False,
    "ignoreUnimportantViews": False,
    "disableWindowAnimation": True,
    
    # 内存管理
    "clearSystemFiles": True,
    "noReset": True,
    "fullReset": False,
    
    # 日志配置
    "systemPort": 8200,  # 避免端口冲突
    "mjpegServerPort": 7810,
}
```

### 高级稳定性配置
```python
# 额外的稳定性配置
advanced_caps = {
    # UiAutomator2 服务器配置
    "uiautomator2ServerLaunchTimeout": 120000,  # 2分钟启动超时
    "uiautomator2ServerInstallTimeout": 120000,  # 2分钟安装超时
    "uiautomator2ServerReadTimeout": 180000,    # 3分钟读取超时
    
    # 设备配置
    "androidDeviceReadyTimeout": 30,
    "androidInstallTimeout": 90000,
    "adbExecTimeout": 20000,
    
    # 性能配置
    "skipLogcatCapture": True,  # 跳过logcat捕获以提高性能
    "dontStopAppOnReset": True,
    "recreateChromeDriverSessions": True,
    
    # 网络配置
    "networkSpeed": "full",
    "gpsEnabled": False,
    "locationServicesEnabled": False,
}
```

## 🛠️ 预防措施

### 1. 定期重启应用
```python
# 每隔一定时间重启应用
async def periodic_app_restart(driver, interval_minutes=30):
    """定期重启应用以防止内存泄漏"""
    while True:
        await asyncio.sleep(interval_minutes * 60)
        try:
            logger.info("🔄 定期重启应用")
            driver.terminate_app("com.google.android.youtube")
            await asyncio.sleep(3)
            driver.activate_app("com.google.android.youtube")
            await asyncio.sleep(5)
        except Exception as e:
            logger.warning(f"定期重启失败: {e}")
```

### 2. 内存监控
```python
# 监控设备内存使用
def check_device_memory(driver):
    """检查设备内存使用情况"""
    try:
        memory_info = driver.get_performance_data("com.google.android.youtube", "memoryinfo", 5)
        logger.info(f"内存使用情况: {memory_info}")
        return memory_info
    except Exception as e:
        logger.warning(f"无法获取内存信息: {e}")
        return None
```

### 3. 连接健康检查
```python
async def health_check(driver):
    """检查Appium连接健康状态"""
    try:
        # 测试基本操作
        driver.current_activity
        driver.get_window_size()
        return True
    except Exception as e:
        logger.warning(f"健康检查失败: {e}")
        return False
```

## 🚨 故障排除

### 常见错误及解决方案

1. **UiAutomator2服务崩溃**
   - 原因: 长时间运行、内存不足
   - 解决: 自动重启应用、定期清理内存

2. **元素查找超时**
   - 原因: 界面加载慢、网络延迟
   - 解决: 增加超时时间、添加重试机制

3. **设备连接丢失**
   - 原因: USB连接不稳定、ADB异常
   - 解决: 重新连接设备、重启ADB服务

### 监控脚本
```python
import psutil
import time

def monitor_appium_process():
    """监控Appium进程状态"""
    for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
        if 'node' in proc.info['name'].lower() and 'appium' in ' '.join(proc.cmdline()):
            memory_mb = proc.info['memory_info'].rss / 1024 / 1024
            logger.info(f"Appium进程内存使用: {memory_mb:.2f} MB")
            
            # 如果内存使用超过1GB，建议重启
            if memory_mb > 1024:
                logger.warning("Appium内存使用过高，建议重启")
```

## 📊 性能优化建议

1. **减少不必要的操作**
   - 避免频繁获取页面源码
   - 减少截图操作
   - 优化元素查找策略

2. **合理设置超时时间**
   - 根据设备性能调整超时
   - 区分不同操作的超时时间
   - 避免过长的等待时间

3. **使用高效的定位策略**
   - 优先使用resource-id
   - 避免复杂的xpath表达式
   - 减少坐标操作

4. **定期清理资源**
   - 定期重启应用
   - 清理临时文件
   - 释放不必要的内存
