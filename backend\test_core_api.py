#!/usr/bin/env python3
"""
测试Core服务API
手动启动第二个子任务
"""

import asyncio
import aiohttp
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_core_api():
    """测试Core服务API"""
    try:
        # Core服务地址
        core_url = "http://localhost:8001"
        
        # 第二个子任务ID
        task_id = "1169ab30-66ee-4357-a3ea-156626b8eabb"
        
        # 构造任务数据
        task_data = {
            "task_id": task_id,
            "platform_id": "youtube",
            "account_id": "68230613013b7bb376ec174c",
            "device_id": "85",
            "content_path": "H:\\PublishSystem\\youtube\\B-HK-1-2-23-002\\歴史の奥深くに佇む唐の美女たち——お気に入りはどの子ですか？コメントで教えてくださいね-4.mp4",
            "video_file": "H:\\PublishSystem\\youtube\\B-HK-1-2-23-002\\歴史の奥深くに佇む唐の美女たち——お気に入りはどの子ですか？コメントで教えてくださいね-4.mp4",
            "task_type": "subtask",
            "subtask_index": 2,
            "metadata": {
                "titleTemplate": "{filename} - 精彩视频",
                "description": "这是一个自动生成的描述",
                "contentType": "shorts",
                "privacyStatus": "unlisted"
            }
        }
        
        logger.info(f"测试Core服务API: {core_url}")
        logger.info(f"任务数据: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 1. 测试Core服务是否在线
                logger.info("1. 测试Core服务连接...")
                async with session.get(
                    f"{core_url}/health",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    logger.info(f"Core服务健康检查: {response.status}")
                    
            except Exception as e:
                logger.error(f"Core服务连接失败: {str(e)}")
                return
                
            try:
                # 2. 创建任务
                logger.info("2. 创建任务...")
                async with session.post(
                    f"{core_url}/api/tasks",
                    json=task_data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"创建任务响应: {response.status} - {response_text}")
                    
                    if response.status != 200:
                        logger.error("创建任务失败")
                        return
                        
            except Exception as e:
                logger.error(f"创建任务异常: {str(e)}")
                return
                
            try:
                # 3. 启动任务
                logger.info("3. 启动任务...")
                async with session.post(
                    f"{core_url}/api/tasks/{task_id}/start",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"启动任务响应: {response.status} - {response_text}")
                    
                    if response.status == 200:
                        logger.info("✅ 任务启动成功")
                    else:
                        logger.error("❌ 任务启动失败")
                        
            except Exception as e:
                logger.error(f"启动任务异常: {str(e)}")
                
            try:
                # 4. 检查任务状态
                logger.info("4. 检查任务状态...")
                await asyncio.sleep(2)  # 等待2秒
                
                async with session.get(
                    f"{core_url}/api/tasks/{task_id}/status",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"任务状态响应: {response.status} - {response_text}")
                    
            except Exception as e:
                logger.error(f"检查任务状态异常: {str(e)}")
                
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(test_core_api())
