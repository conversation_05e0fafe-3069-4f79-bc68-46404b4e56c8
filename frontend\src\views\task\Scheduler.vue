<template>
  <div class="task-scheduler">
    <div class="scheduler-header">
      <h1>📋 任务调度</h1>
      <p class="header-description">管理和监控所有任务的执行状态</p>
    </div>

    <div class="scheduler-content">
      <!-- 统计卡片 -->
      <el-row :gutter="16" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon running">🚀</div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.running }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon pending">⏳</div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.pending }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon completed">✅</div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon failed">❌</div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.failed }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 任务列表 -->
      <el-card class="task-list-card">
        <template #header>
          <div class="card-header">
            <span>📝 任务列表</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="refreshTasks" :loading="loading">
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table :data="tasks" v-loading="loading" stripe>
          <el-table-column prop="id" label="任务ID" width="120" show-overflow-tooltip />
          <el-table-column prop="platform_name" label="平台" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.platform_name || getPlatformName(row.platform_id) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="account_name" label="账号" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :title="row.account_name || row.account_id">{{ row.account_name || row.account_id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress || 0"
                :status="getProgressStatus(row.status)"
                :stroke-width="6"
                text-inside
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'pending'"
                type="primary"
                size="small"
                @click="startTaskAction(row.id)"
              >
                开始
              </el-button>
              <el-button
                v-if="row.status === 'running'"
                type="warning"
                size="small"
                @click="pauseTaskAction(row.id)"
              >
                暂停
              </el-button>
              <el-button
                v-if="row.status === 'running' || row.status === 'paused'"
                type="danger"
                size="small"
                @click="cancelTaskAction(row.id)"
              >
                取消
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewTaskDetails(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任务详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedTask.platform_name || getPlatformName(selectedTask.platform_id) }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ selectedTask.account_name || selectedTask.account_id }}</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ selectedTask.content_path }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(selectedTask.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="进度" :span="2">
            <el-progress
              :percentage="selectedTask.progress || 0"
              :status="getProgressStatus(selectedTask.status)"
            />
          </el-descriptions-item>
        </el-descriptions>

        <!-- 任务日志 -->
        <div class="task-logs" style="margin-top: 20px;">
          <h4>执行日志</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in taskLogs"
              :key="index"
              :timestamp="log.timestamp"
              :type="getLogType(log.level)"
              size="small"
            >
              {{ log.message }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="selectedTask && selectedTask.status === 'completed'"
          type="primary"
          @click="viewTaskResult"
        >
          查看结果
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTaskList, getTaskStats, getTaskLogs, startTask, pauseTask, cancelTask } from '@/api/task'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const detailDialogVisible = ref(false)
const selectedTask = ref(null)
const taskLogs = ref([])

// 统计数据
const stats = reactive({
  running: 0,
  pending: 0,
  completed: 0,
  failed: 0
})

// 自动刷新定时器
let refreshTimer = null

// 初始化
onMounted(() => {
  fetchTasks()
  startAutoRefresh()
})

// 清理
onBeforeUnmount(() => {
  stopAutoRefresh()
})

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true
    console.log('获取任务列表...')

    // 调用真实的API获取任务列表
    const response = await getTaskList({
      limit: 50,
      offset: 0
    })

    console.log('任务列表响应:', response)

    if (response && response.data) {
      tasks.value = response.data.tasks || []
      console.log('任务列表数据:', tasks.value)
    } else {
      console.warn('API响应数据格式异常，使用空数组')
      tasks.value = []
    }

    // 获取统计数据
    await fetchStats()

  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.warning('任务管理功能开发中，暂时显示空数据')
    tasks.value = []
    // 设置默认统计数据
    Object.assign(stats, {
      running: 0,
      pending: 0,
      completed: 0,
      failed: 0
    })
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    console.log('获取任务统计...')

    const response = await getTaskStats()
    console.log('统计数据响应:', response)

    if (response && response.data && response.data.status_stats) {
      const statusStats = response.data.status_stats
      Object.assign(stats, {
        running: statusStats.running || 0,
        pending: statusStats.pending || 0,
        completed: statusStats.completed || 0,
        failed: statusStats.failed || 0
      })
      console.log('更新统计数据:', stats)
    } else {
      console.warn('统计数据格式异常，使用默认值')
      // 设置默认统计数据
      Object.assign(stats, {
        running: 0,
        pending: 0,
        completed: 0,
        failed: 0
      })
    }

  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 设置默认统计数据
    Object.assign(stats, {
      running: 0,
      pending: 0,
      completed: 0,
      failed: 0
    })
  }
}

// 更新统计数据
const updateStats = () => {
  stats.running = tasks.value.filter(t => t.status === 'running').length
  stats.pending = tasks.value.filter(t => t.status === 'pending').length
  stats.completed = tasks.value.filter(t => t.status === 'completed').length
  stats.failed = tasks.value.filter(t => t.status === 'failed').length
}

// 刷新任务
const refreshTasks = () => {
  fetchTasks()
}

// 开始任务
const startTaskAction = async (taskId) => {
  try {
    await ElMessageBox.confirm('确定要开始执行此任务吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    console.log('开始任务:', taskId)
    await startTask(taskId)
    ElMessage.success('任务已开始执行')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始任务失败:', error)
      ElMessage.error('开始任务失败')
    }
  }
}

// 暂停任务
const pauseTaskAction = async (taskId) => {
  try {
    await ElMessageBox.confirm('确定要暂停此任务吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    console.log('暂停任务:', taskId)
    await pauseTask(taskId)
    ElMessage.success('任务已暂停')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停任务失败:', error)
      ElMessage.error('暂停任务失败')
    }
  }
}

// 取消任务
const cancelTaskAction = async (taskId) => {
  try {
    await ElMessageBox.confirm('确定要取消此任务吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    console.log('取消任务:', taskId)
    await cancelTask(taskId)
    ElMessage.success('任务已取消')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

// 查看任务详情
const viewTaskDetails = async (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true

  // 获取任务日志
  try {
    const logs = await getTaskLogs(task.id)
    taskLogs.value = logs || []
  } catch (error) {
    console.error('获取任务日志失败:', error)
    taskLogs.value = []
  }
}

// 查看任务结果
const viewTaskResult = () => {
  if (!selectedTask.value) return

  // 跳转到通用的任务结果页面
  window.open(`/tasks/result?taskId=${selectedTask.value.id}`, '_blank')
}

// 自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchTasks()
  }, 10000) // 每10秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 工具函数
const getPlatformName = (platformId) => {
  const platforms = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'instagram': 'Instagram'
  }
  return platforms[platformId] || platformId
}

const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'primary',
    'paused': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '运行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning',
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}
</script>

<style scoped>
.task-scheduler {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.scheduler-header {
  margin-bottom: 20px;
}

.scheduler-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  width: 60px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-icon.running {
  color: #409EFF;
}

.stat-icon.pending {
  color: #E6A23C;
}

.stat-icon.completed {
  color: #67C23A;
}

.stat-icon.failed {
  color: #F56C6C;
}

.task-list-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #409EFF;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.task-logs {
  max-height: 300px;
  overflow-y: auto;
}

.task-logs h4 {
  margin: 0 0 15px 0;
  color: #409EFF;
}
</style>