#!/usr/bin/env python3
"""
手动启动子任务脚本
用于测试子任务启动机制
"""

import asyncio
import aiohttp
import json
import logging
from pymongo import MongoClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def start_subtask_in_core(task_id: str):
    """在Core服务中启动子任务"""
    try:
        # 连接MongoDB获取任务信息
        mongo_client = MongoClient("mongodb://***************:27017")
        mongo_db = mongo_client.thunderhub
        
        # 获取子任务信息
        subtask = mongo_db.social_tasks.find_one({"task_id": task_id})
        if not subtask:
            logger.error(f"找不到子任务: {task_id}")
            return False
            
        logger.info(f"找到子任务: {task_id}")
        logger.info(f"子任务信息: {subtask}")
        
        # Core服务的API地址
        core_url = "http://localhost:8001"
        
        # 构造请求数据
        request_data = {
            "task_id": task_id,
            "platform_id": subtask.get("platform_id"),
            "account_id": subtask.get("account_id"),
            "device_id": subtask.get("device_id"),
            "content_path": subtask.get("content_path"),
            "metadata": subtask.get("metadata", {}),
            "task_type": subtask.get("task_type", "subtask"),
            "subtask_index": subtask.get("subtask_index"),
            "video_file": subtask.get("video_file"),
            "selected_files": subtask.get("selected_files", []),
            "schedule_type": subtask.get("schedule_type", "immediate"),
            "workflow_id": subtask.get("workflow_id"),
            "status": "pending",
            "created_at": subtask.get("created_at"),
            "parent_task_id": subtask.get("parent_task_id")
        }
        
        logger.info(f"准备发送到Core服务的数据: {json.dumps(request_data, indent=2, default=str)}")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 先创建任务
                logger.info(f"创建任务: POST {core_url}/api/tasks")
                async with session.post(
                    f"{core_url}/api/tasks",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_text = await response.text()
                    logger.info(f"创建任务响应: {response.status} - {response_text}")
                    
                    if response.status == 200:
                        logger.info(f"Core服务任务{task_id}创建成功")
                        
                        # 然后启动任务
                        logger.info(f"启动任务: POST {core_url}/api/tasks/{task_id}/start")
                        async with session.post(
                            f"{core_url}/api/tasks/{task_id}/start",
                            timeout=aiohttp.ClientTimeout(total=30)
                        ) as start_response:
                            start_response_text = await start_response.text()
                            logger.info(f"启动任务响应: {start_response.status} - {start_response_text}")
                            
                            if start_response.status == 200:
                                logger.info(f"Core服务任务{task_id}启动成功")
                                return True
                            else:
                                logger.error(f"Core服务任务{task_id}启动失败: {start_response.status}")
                                return False
                    else:
                        logger.error(f"Core服务任务{task_id}创建失败: {response.status} - {response_text}")
                        return False
                        
            except aiohttp.ClientError as e:
                logger.error(f"HTTP请求失败: {str(e)}")
                return False
                
    except Exception as e:
        logger.error(f"启动子任务失败: {str(e)}", exc_info=True)
        return False
    finally:
        if 'mongo_client' in locals():
            mongo_client.close()


async def main():
    """主函数"""
    # 要启动的子任务ID
    task_id = "41670aab-76f2-4788-b6b7-810ac0802580"
    
    logger.info(f"开始手动启动子任务: {task_id}")
    
    success = await start_subtask_in_core(task_id)
    
    if success:
        logger.info("✅ 子任务启动成功")
    else:
        logger.error("❌ 子任务启动失败")


if __name__ == "__main__":
    asyncio.run(main())
