#!/usr/bin/env python3
"""
测试YouTube Shorts工作流步骤顺序
"""

import yaml
import os

def test_workflow_order():
    """测试工作流步骤顺序"""
    print("🔧 测试YouTube Shorts工作流步骤顺序...")
    
    # 加载工作流配置
    workflow_path = os.path.join(
        os.path.dirname(__file__), 
        'config', 'platforms', 'youtube', 'workflows', 'shorts_upload.yaml'
    )
    
    if not os.path.exists(workflow_path):
        print(f"❌ 工作流文件不存在: {workflow_path}")
        return
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        workflow_config = yaml.safe_load(f)
    
    steps = workflow_config['workflow']['steps']
    
    print(f"📋 工作流包含 {len(steps)} 个步骤:")
    print()
    
    # 查找关键步骤的位置
    step_positions = {}
    for i, step in enumerate(steps):
        step_id = step.get('id', 'unknown')
        step_positions[step_id] = i + 1
        
        step_name = step.get('name', 'Unknown')
        required = step.get('required', False)
        action = step.get('action', 'unknown')
        
        print(f"{i+1:2d}. {step_name}")
        print(f"    ID: {step_id}")
        print(f"    动作: {action}")
        print(f"    必需: {'是' if required else '否'}")
        print()
    
    # 验证关键步骤的顺序
    print("🔍 验证关键步骤顺序:")
    
    # 定义期望的步骤顺序
    expected_order = [
        'music_handling',      # 音乐处理
        'audio_settings',      # 音频设置
        'go_to_editor',        # 前往编辑器
        'input_title',         # 输入标题
        'input_description',   # 输入描述
        'set_privacy',         # 设置隐私
        'set_publish_time',    # 设置发布时间
        'back_to_main',        # 返回主页面
        'final_upload'         # 完成上传
    ]
    
    # 检查步骤是否存在且顺序正确
    missing_steps = []
    order_issues = []
    
    for i, step_id in enumerate(expected_order):
        if step_id not in step_positions:
            missing_steps.append(step_id)
        else:
            current_pos = step_positions[step_id]
            print(f"✅ {step_id}: 位置 {current_pos}")
            
            # 检查与前一个步骤的顺序
            if i > 0:
                prev_step_id = expected_order[i-1]
                if prev_step_id in step_positions:
                    prev_pos = step_positions[prev_step_id]
                    if current_pos <= prev_pos:
                        order_issues.append(f"{prev_step_id} (位置{prev_pos}) 应该在 {step_id} (位置{current_pos}) 之前")
    
    # 报告结果
    print("\n📊 检查结果:")
    
    if missing_steps:
        print("❌ 缺少的步骤:")
        for step in missing_steps:
            print(f"   - {step}")
    
    if order_issues:
        print("❌ 顺序问题:")
        for issue in order_issues:
            print(f"   - {issue}")
    
    if not missing_steps and not order_issues:
        print("✅ 所有关键步骤都存在且顺序正确")
    
    # 特别检查音频设置和前往编辑器的顺序
    print("\n🎵 特别检查音频设置流程:")
    
    audio_pos = step_positions.get('audio_settings')
    editor_pos = step_positions.get('go_to_editor')
    
    if audio_pos and editor_pos:
        if audio_pos < editor_pos:
            print(f"✅ 音频设置 (位置{audio_pos}) 在 前往编辑器 (位置{editor_pos}) 之前")
            print("✅ 这样音频设置步骤不会错误地点击编辑器下一步按钮")
        else:
            print(f"❌ 顺序错误: 音频设置 (位置{audio_pos}) 应该在 前往编辑器 (位置{editor_pos}) 之前")
    
    print("\n🎉 工作流步骤顺序测试完成")

if __name__ == "__main__":
    test_workflow_order()
