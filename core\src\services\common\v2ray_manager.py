"""
V2rayN管理器
负责V2rayN应用的启动和连接管理
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logger = logging.getLogger(__name__)


class V2rayManager:
    """V2rayN管理器类"""

    def __init__(self, device_manager):
        """初始化V2rayN管理器

        Args:
            device_manager: 设备管理器实例
        """
        self.device_manager = device_manager
        # 使用设备管理器的实际设备ID（映射后的）
        self.device_id = device_manager.device_id
        self.package = 'com.v2ray.ang'
        self.activity = 'com.v2ray.ang.ui.MainActivity'

    async def launch_and_connect(self) -> bool:
        """启动V2rayN应用并连接

        Returns:
            bool: 是否成功启动并连接
        """
        try:
            logger.info("===== 开始启动V2rayN应用 =====")

            # 首先检查应用是否已安装
            if not await self._check_app_installed():
                logger.warning(f"设备上未找到V2rayN应用: {self.package}")
                return False

            # 尝试使用Appium方式启动
            if self.device_manager.is_connected():
                success = await self._launch_with_appium()
                if success:
                    return await self._connect_proxy()

            # 如果Appium方式失败，尝试ADB方式
            logger.info("尝试使用ADB方式启动V2rayN")
            success = await self._launch_with_adb()
            if success:
                # ADB方式启动后，如果有Appium连接，尝试操作界面
                if self.device_manager.is_connected():
                    return await self._connect_proxy()
                else:
                    # 没有Appium连接，只能假设启动成功
                    logger.info("V2rayN应用已通过ADB启动，但无法验证连接状态")
                    return True

            return False

        except Exception as e:
            logger.error(f"启动V2rayN应用异常: {str(e)}", exc_info=True)
            return False

    async def _check_app_installed(self) -> bool:
        """检查V2rayN应用是否已安装

        Returns:
            bool: 是否已安装
        """
        try:
            # 获取最新的设备ID（防止设备管理器连接后ID发生变化）
            current_device_id = self.device_manager.device_id
            logger.info(f"检查V2rayN应用是否安装，设备ID: {current_device_id}, 包名: {self.package}")

            list_process = await asyncio.create_subprocess_shell(
                f"adb -s {current_device_id} shell pm list packages",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            list_stdout, list_stderr = await list_process.communicate()

            logger.info(f"ADB命令返回码: {list_process.returncode}")
            if list_stderr:
                logger.warning(f"ADB命令错误输出: {list_stderr.decode()}")

            packages_list = list_stdout.decode().splitlines()
            logger.info(f"总共找到 {len(packages_list)} 个应用包")

            # 查找V2rayN相关的包
            v2ray_packages = []
            for pkg in packages_list:
                if 'v2ray' in pkg.lower() or 'ang' in pkg.lower():
                    v2ray_packages.append(pkg)
                    logger.info(f"找到V2rayN相关包: {pkg}")

                # 检查包名是否包含目标包名（处理package:前缀）
                if self.package in pkg:
                    logger.info(f"✅ 找到目标V2rayN应用: {pkg}")
                    return True

            if v2ray_packages:
                logger.warning(f"找到V2rayN相关包但不是目标包 {self.package}: {v2ray_packages}")
            else:
                logger.warning("没有找到任何V2rayN相关的应用包")

            # 显示前10个包用于调试
            logger.info("前10个应用包:")
            for i, pkg in enumerate(packages_list[:10]):
                logger.info(f"  {i+1}: {pkg}")

            return False

        except Exception as e:
            logger.error(f"检查应用安装状态异常: {str(e)}", exc_info=True)
            return False

    async def _launch_with_appium(self) -> bool:
        """使用Appium方式启动V2rayN

        Returns:
            bool: 是否成功启动
        """
        try:
            logger.info("使用Appium方式启动V2rayN")
            success = await self.device_manager.launch_app(self.package, self.activity)
            if success:
                logger.info("V2rayN应用已通过Appium启动")
                await asyncio.sleep(3)
                return True
            return False

        except Exception as e:
            logger.warning(f"使用Appium启动V2rayN失败: {str(e)}")
            return False

    async def _launch_with_adb(self) -> bool:
        """使用ADB方式启动V2rayN

        Returns:
            bool: 是否成功启动
        """
        try:
            # 获取最新的设备ID
            current_device_id = self.device_manager.device_id
            logger.info(f"使用ADB启动V2rayN，设备ID: {current_device_id}")

            start_process = await asyncio.create_subprocess_shell(
                f"adb -s {current_device_id} shell am start -n {self.package}/{self.activity}",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            start_stdout, start_stderr = await start_process.communicate()

            if start_process.returncode != 0:
                logger.warning(f"启动V2rayN应用失败: {start_stderr.decode() if start_stderr else start_stdout.decode()}")
                return False

            logger.info("V2rayN应用已通过ADB启动")
            await asyncio.sleep(5)
            return True

        except Exception as e:
            logger.warning(f"使用ADB启动V2rayN失败: {str(e)}")
            return False

    async def _connect_proxy(self) -> bool:
        """连接代理

        Returns:
            bool: 是否成功连接
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                logger.warning("没有可用的Appium驱动，无法操作V2rayN界面")
                return True  # 假设启动成功

            logger.info("尝试操作V2rayN界面进行连接")

            # 查找并点击FAB按钮
            try:
                logger.info("查找V2rayN的FAB按钮")
                fab_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((AppiumBy.ID, 'com.v2ray.ang:id/fab'))
                )
                fab_button.click()
                logger.info("已点击V2rayN的FAB按钮")
                await asyncio.sleep(2)

                # 检查连接状态
                try:
                    status_element = driver.find_element(AppiumBy.ID, 'com.v2ray.ang:id/tv_test_state')
                    status_text = status_element.text
                    logger.info(f"V2rayN连接状态: {status_text}")

                    if "成功" in status_text or "connected" in status_text.lower():
                        logger.info("V2rayN已成功连接")
                        return True
                    else:
                        logger.warning(f"V2rayN连接状态异常: {status_text}")
                        return True  # 即使状态异常，也认为启动成功
                except Exception as status_error:
                    logger.warning(f"无法获取V2rayN连接状态: {str(status_error)}")
                    return True  # 无法获取状态，假设成功

            except Exception as fab_error:
                logger.warning(f"无法找到或点击V2rayN的FAB按钮: {str(fab_error)}")
                return True  # 即使无法操作界面，也认为应用已启动

        except Exception as e:
            logger.warning(f"操作V2rayN界面异常: {str(e)}")
            return True  # 即使操作失败，也认为应用已启动

    async def get_connection_status(self) -> Dict[str, Any]:
        """获取V2rayN连接状态

        Returns:
            Dict[str, Any]: 连接状态信息
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                return {
                    "status": "unknown",
                    "message": "无法获取连接状态：没有可用的Appium驱动"
                }

            # 尝试获取状态文本
            try:
                status_element = driver.find_element(AppiumBy.ID, 'com.v2ray.ang:id/tv_test_state')
                status_text = status_element.text

                if "成功" in status_text or "connected" in status_text.lower():
                    return {
                        "status": "connected",
                        "message": f"V2rayN已连接: {status_text}"
                    }
                else:
                    return {
                        "status": "disconnected",
                        "message": f"V2rayN未连接: {status_text}"
                    }
            except Exception as e:
                return {
                    "status": "unknown",
                    "message": f"无法获取连接状态: {str(e)}"
                }

        except Exception as e:
            logger.error(f"获取V2rayN连接状态异常: {str(e)}")
            return {
                "status": "error",
                "message": f"获取连接状态异常: {str(e)}"
            }
