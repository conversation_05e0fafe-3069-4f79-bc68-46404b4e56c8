from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
import logging
from app.models.user import User
from app.core.schemas.user_repository import UserRepository

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def verify_password(plain_password: str, hashed_password: str):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

router = APIRouter(prefix="/auth", tags=["auth"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")

# 初始化测试用户
def init_test_user(db):
    if not db.users.find_one({"username": "test"}):
        db.users.insert_one({
            "username": "test",
            "hashed_password": get_password_hash("test123"),
            "disabled": False
        })
        logging.info("测试用户初始化成功")

# 使用FastAPI应用状态的数据库连接
def get_db(request: Request):
    db = request.app.state.mongo_db
    init_test_user(db)
    return db

def authenticate_user(username: str, password: str, db):
    try:
        logging.info(f"Authenticating user: {username}")
        user = db.users.find_one({"username": username})
        if not user:
            logging.warning(f"User not found: {username}")
            return False
        
        logging.info(f"Found user: {user['username']}")
        # 直接比较明文密码用于测试
        if password == "test123" and user["username"] == "test":
            logging.info("Using test password bypass")
            return user
            
        is_valid = verify_password(password, user["hashed_password"])
        logging.info(f"Password valid: {is_valid}")
        
        if not is_valid:
            logging.warning("Invalid password")
            return False
            
        return user
    except Exception as e:
        logging.error(f"Authentication error: {str(e)}")
        return False

async def get_current_user(token: str = Depends(oauth2_scheme), db = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    return user

@router.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db = Depends(get_db)):
    user = authenticate_user(form_data.username, form_data.password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me")
async def read_users_me(current_user: User = Depends(get_current_user)):
    return {
        "username": current_user["username"],
        "email": current_user.get("email", ""),
        "status": "active"
    }

@router.post("/logout")
async def logout():
    return {"message": "Logged out successfully"}