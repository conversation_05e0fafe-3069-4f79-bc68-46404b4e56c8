# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: task.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\ntask.proto\x12\x04task\"\xe3\x01\n\x0bTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x13\n\x0bplatform_id\x18\x02 \x01(\t\x12\x12\n\naccount_id\x18\x03 \x01(\t\x12\x11\n\tdevice_id\x18\x04 \x01(\t\x12\x14\n\x0c\x63ontent_path\x18\x05 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x06 \x01(\t\x12-\n\x06params\x18\x07 \x03(\x0b\x32\x1d.task.TaskRequest.ParamsEntry\x1a-\n\x0bParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\" \n\rTaskIdRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"?\n\x0cTaskResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07task_id\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"\xbd\x01\n\x12TaskStatusResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08progress\x18\x03 \x01(\x05\x12\x12\n\nstart_time\x18\x04 \x01(\t\x12\x1a\n\x12\x65stimated_end_time\x18\x05 \x01(\t\x12\'\n\x0c\x64\x65vice_usage\x18\x06 \x01(\x0b\x32\x11.task.DeviceUsage\x12\x1b\n\x04logs\x18\x07 \x03(\x0b\x32\r.task.TaskLog\";\n\x0b\x44\x65viceUsage\x12\x0b\n\x03\x63pu\x18\x01 \x01(\x05\x12\x0e\n\x06memory\x18\x02 \x01(\x05\x12\x0f\n\x07network\x18\x03 \x01(\t\"<\n\x07TaskLog\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\r\n\x05level\x18\x02 \x01(\t\x12\x11\n\ttimestamp\x18\x03 \x01(\t\"@\n\x10TaskLogsResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x1b\n\x04logs\x18\x02 \x03(\x0b\x32\r.task.TaskLog2\xed\x02\n\x0bTaskService\x12\x35\n\nCreateTask\x12\x11.task.TaskRequest\x1a\x12.task.TaskResponse\"\x00\x12\x36\n\tStartTask\x12\x13.task.TaskIdRequest\x1a\x12.task.TaskResponse\"\x00\x12\x36\n\tPauseTask\x12\x13.task.TaskIdRequest\x1a\x12.task.TaskResponse\"\x00\x12\x37\n\nCancelTask\x12\x13.task.TaskIdRequest\x1a\x12.task.TaskResponse\"\x00\x12@\n\rGetTaskStatus\x12\x13.task.TaskIdRequest\x1a\x18.task.TaskStatusResponse\"\x00\x12<\n\x0bGetTaskLogs\x12\x13.task.TaskIdRequest\x1a\x16.task.TaskLogsResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _TASKREQUEST_PARAMSENTRY._options = None
  _TASKREQUEST_PARAMSENTRY._serialized_options = b'8\001'
  _globals['_TASKREQUEST']._serialized_start=21
  _globals['_TASKREQUEST']._serialized_end=248
  _globals['_TASKREQUEST_PARAMSENTRY']._serialized_start=203
  _globals['_TASKREQUEST_PARAMSENTRY']._serialized_end=248
  _globals['_TASKIDREQUEST']._serialized_start=250
  _globals['_TASKIDREQUEST']._serialized_end=282
  _globals['_TASKRESPONSE']._serialized_start=284
  _globals['_TASKRESPONSE']._serialized_end=347
  _globals['_TASKSTATUSRESPONSE']._serialized_start=350
  _globals['_TASKSTATUSRESPONSE']._serialized_end=539
  _globals['_DEVICEUSAGE']._serialized_start=541
  _globals['_DEVICEUSAGE']._serialized_end=600
  _globals['_TASKLOG']._serialized_start=602
  _globals['_TASKLOG']._serialized_end=662
  _globals['_TASKLOGSRESPONSE']._serialized_start=664
  _globals['_TASKLOGSRESPONSE']._serialized_end=728
  _globals['_TASKSERVICE']._serialized_start=731
  _globals['_TASKSERVICE']._serialized_end=1096
# @@protoc_insertion_point(module_scope)
