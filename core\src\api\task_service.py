"""
任务服务gRPC实现
"""

import logging
import asyncio
import datetime
from typing import Dict, List, Any, Optional

from src.main_service import CoreMainService
from src.services.task_executor import TaskExecutor
from src.api import task_pb2
from src.api import task_pb2_grpc

logger = logging.getLogger(__name__)

class TaskServiceImpl(task_pb2_grpc.TaskServiceServicer):
    """任务服务gRPC实现类"""
    
    def __init__(self, main_service: CoreMainService):
        """初始化任务服务
        
        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.task_executor = TaskExecutor(main_service)
        logger.info("任务服务gRPC实现初始化")
    
    async def CreateTask(self, request, context):
        """创建任务"""
        try:
            logger.info(f"收到创建任务请求: {request.task_id}")
            
            # 构建任务数据
            task_data = {
                "task_id": request.task_id,
                "platform_id": request.platform_id,
                "account_id": request.account_id,
                "device_id": request.device_id,
                "content_path": request.content_path,
                "status": "pending",
                "created_at": datetime.datetime.now().isoformat()
            }
            
            # 添加可选字段
            if request.workflow_id:
                task_data["workflow_id"] = request.workflow_id
            
            # 添加任务参数
            if request.params:
                task_data["params"] = {}
                for key, value in request.params.items():
                    task_data["params"][key] = value
            
            # 创建任务
            success = await self.task_executor.create_task(task_data)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = request.task_id
            
            if not success:
                response.error = "创建任务失败"
            
            return response
            
        except Exception as e:
            logger.error(f"创建任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"创建任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def StartTask(self, request, context):
        """开始执行任务"""
        try:
            task_id = request.task_id
            logger.info(f"收到开始执行任务请求: {task_id}")
            
            # 开始执行任务
            success = await self.task_executor.start_task(task_id)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id
            
            if not success:
                response.error = f"开始执行任务{task_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"开始执行任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"开始执行任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def PauseTask(self, request, context):
        """暂停任务"""
        try:
            task_id = request.task_id
            logger.info(f"收到暂停任务请求: {task_id}")
            
            # 暂停任务
            success = await self.task_executor.pause_task(task_id)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id
            
            if not success:
                response.error = f"暂停任务{task_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"暂停任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"暂停任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def CancelTask(self, request, context):
        """取消任务"""
        try:
            task_id = request.task_id
            logger.info(f"收到取消任务请求: {task_id}")
            
            # 取消任务
            success = await self.task_executor.cancel_task(task_id)
            
            response = task_pb2.TaskResponse()
            response.success = success
            response.task_id = task_id
            
            if not success:
                response.error = f"取消任务{task_id}失败"
            
            return response
            
        except Exception as e:
            logger.error(f"取消任务异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"取消任务失败: {str(e)}")
            return task_pb2.TaskResponse(success=False, error=str(e))
    
    async def GetTaskStatus(self, request, context):
        """获取任务状态"""
        try:
            task_id = request.task_id
            logger.info(f"收到获取任务状态请求: {task_id}")
            
            # 获取任务状态
            status = await self.task_executor.get_task_status(task_id)
            
            if not status:
                context.set_code(5)  # NOT_FOUND
                context.set_details(f"任务{task_id}不存在")
                return task_pb2.TaskStatusResponse()
            
            # 构建响应
            response = task_pb2.TaskStatusResponse()
            response.task_id = task_id
            response.status = status.get("status", "unknown")
            response.progress = status.get("progress", 0)
            response.start_time = status.get("start_time", "")
            response.estimated_end_time = status.get("estimated_end_time", "")
            
            # 设备使用情况
            device_usage = status.get("device_usage", {})
            response.device_usage.cpu = device_usage.get("cpu", 0)
            response.device_usage.memory = device_usage.get("memory", 0)
            response.device_usage.network = device_usage.get("network", "未知")
            
            # 最近日志
            logs = status.get("logs", [])
            for log in logs:
                log_entry = response.logs.add()
                log_entry.message = log.get("message", "")
                log_entry.level = log.get("level", "info")
                log_entry.timestamp = log.get("timestamp", "")
            
            return response
            
        except Exception as e:
            logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取任务状态失败: {str(e)}")
            return task_pb2.TaskStatusResponse()
    
    async def GetTaskLogs(self, request, context):
        """获取任务日志"""
        try:
            task_id = request.task_id
            logger.info(f"收到获取任务日志请求: {task_id}")
            
            # 获取任务日志
            logs = await self.task_executor.get_task_logs(task_id)
            
            # 构建响应
            response = task_pb2.TaskLogsResponse()
            response.task_id = task_id
            
            for log in logs:
                log_entry = response.logs.add()
                log_entry.message = log.get("message", "")
                log_entry.level = log.get("level", "info")
                log_entry.timestamp = log.get("timestamp", "")
            
            return response
            
        except Exception as e:
            logger.error(f"获取任务日志异常: {str(e)}", exc_info=True)
            context.set_code(13)  # INTERNAL
            context.set_details(f"获取任务日志失败: {str(e)}")
            return task_pb2.TaskLogsResponse()
