# 多视频任务子任务拆分功能修复总结

## 🎯 问题描述

用户选择了2个视频创建任务，但是任务没有正确拆分为子任务，仍然显示为单任务。

## 🔍 问题分析

### 原来的问题
1. **前端未传递选中文件**: 前端将多个文件路径用分号连接成字符串，但没有作为独立字段传递
2. **后端API不支持**: `/api/social/tasks`接口不支持`selected_files`字段和子任务拆分
3. **接口不一致**: YouTube上传API (`/api/v1/social/youtube/uploads`) 支持子任务拆分，但通用任务API不支持

### 问题根源
- 前端调用的是通用任务API (`/api/social/tasks`)，而不是YouTube专用API
- 通用任务API缺少多文件处理逻辑
- 前端没有正确传递`selected_files`参数

## 🛠️ 修复方案

### 1. 前端修复

**修改文件**: `frontend/src/views/social/components/PublishTaskForm.vue`

**问题代码**:
```javascript
// 原来只是将文件路径连接成字符串
form.contentPath = selectedContents.value.map(file => file.path).join(';')

// 提交时没有传递选中文件列表
const taskData = {
  platform_id: form.platform,
  account_id: form.account,
  content_path: form.contentPath,
  // 缺少 selected_files 字段
}
```

**修复代码**:
```javascript
// 构建请求数据
const taskData = {
  platform_id: form.platform,
  core_service_id: form.coreService,
  account_id: form.account,
  content_path: form.contentPath,
  publish_strategy: form.publishStrategy
}

// 如果选择了多个文件，添加选中文件列表
if (selectedContents.value.length > 1) {
  taskData.selected_files = selectedContents.value.map(file => file.path)
  console.log(`检测到多个文件，将创建子任务: ${taskData.selected_files.length} 个文件`)
  console.log('选中的文件:', taskData.selected_files)
} else if (selectedContents.value.length === 1) {
  // 单个文件也添加到selected_files中，保持一致性
  taskData.selected_files = [selectedContents.value[0].path]
  console.log('单个文件:', taskData.selected_files)
}
```

### 2. 接口类型定义修复

**修改文件**: `frontend/src/api/social.ts`

**添加字段**:
```typescript
export interface PublishTask {
  platform_id: string
  account_id: string
  content_path: string
  schedule_type?: 'immediate' | 'scheduled'
  schedule_time?: string
  workflow_id?: string
  selected_files?: string[] // 新增：选中文件列表
}
```

### 3. 后端API修复

**修改文件**: `backend/app/api/social.py`

**原来的代码**:
```python
@router.post("/tasks", response_model=dict)
async def create_publish_task(request: Request, db_service: SocialDatabaseService = Depends(get_social_service)):
    # 只支持单任务创建，不支持子任务拆分
    task_id = str(uuid.uuid4())
    db_task_id = db_service.create_task({
        "task_id": task_id,
        "platform_id": task_data["platform_id"],
        "account_id": task_data["account_id"],
        "content_path": task_data["content_path"],
        # 没有处理 selected_files
    })
```

**修复代码**:
```python
@router.post("/tasks", response_model=dict)
async def create_publish_task(request: Request, db_service: SocialDatabaseService = Depends(get_social_service)):
    # 检查是否需要拆分子任务
    selected_files = task_data.get("selected_files", [])
    if selected_files and len(selected_files) > 1:
        logger.info(f"检测到多个文件，将拆分为子任务: {len(selected_files)} 个文件")
        
        # 创建主任务
        main_task_id = str(uuid.uuid4())
        main_task_data = {
            "task_id": main_task_id,
            "task_type": "main",
            "total_subtasks": len(selected_files),
            "completed_subtasks": 0,
            "selected_files": selected_files,
            # ... 其他字段
        }
        db_service.create_task(main_task_data)
        
        # 为每个文件创建子任务
        subtask_ids = []
        for i, video_file in enumerate(selected_files):
            subtask_id = str(uuid.uuid4())
            subtask_data = {
                "task_id": subtask_id,
                "parent_task_id": main_task_id,
                "task_type": "subtask",
                "subtask_index": i + 1,
                "selected_files": [video_file],
                "video_file": video_file,
                # ... 其他字段
            }
            db_service.create_task(subtask_data)
            subtask_ids.append(subtask_id)
        
        return {
            "task_id": main_task_id,
            "status": "pending",
            "task_type": "main",
            "subtask_ids": subtask_ids,
            "total_subtasks": len(selected_files)
        }
    else:
        # 单个文件，创建普通任务
        # ... 单任务逻辑
```

## 📊 修复效果

### 修复前的问题
- ❌ **多文件合并**: 多个文件路径被合并成一个字符串
- ❌ **无子任务拆分**: 不会创建主任务和子任务
- ❌ **任务类型错误**: 多文件任务仍显示为单任务
- ❌ **执行逻辑混乱**: Core服务无法正确处理多文件

### 修复后的改进
- ✅ **正确传递文件列表**: `selected_files`字段包含所有选中文件
- ✅ **自动子任务拆分**: 多文件自动拆分为主任务+子任务
- ✅ **正确的任务类型**: 主任务标记为`main`，子任务标记为`subtask`
- ✅ **完整的任务关系**: 子任务包含`parent_task_id`和`subtask_index`

## 🎯 任务结构设计

### 单文件任务
```json
{
  "task_id": "uuid-1",
  "task_type": "single",
  "selected_files": ["file1.mp4"],
  "video_file": "file1.mp4"
}
```

### 多文件任务结构
**主任务**:
```json
{
  "task_id": "main-uuid",
  "task_type": "main",
  "total_subtasks": 2,
  "completed_subtasks": 0,
  "selected_files": ["file1.mp4", "file2.mp4"]
}
```

**子任务1**:
```json
{
  "task_id": "sub-uuid-1",
  "parent_task_id": "main-uuid",
  "task_type": "subtask",
  "subtask_index": 1,
  "selected_files": ["file1.mp4"],
  "video_file": "file1.mp4"
}
```

**子任务2**:
```json
{
  "task_id": "sub-uuid-2", 
  "parent_task_id": "main-uuid",
  "task_type": "subtask",
  "subtask_index": 2,
  "selected_files": ["file2.mp4"],
  "video_file": "file2.mp4"
}
```

## 🔧 技术实现细节

### 1. 前端文件选择逻辑
```javascript
// 选择多个文件时
if (selectedContents.value.length > 1) {
  // 设置内容路径（用于显示）
  form.contentPath = selectedContents.value.map(file => file.path).join(';')
  
  // 设置选中文件列表（用于后端处理）
  taskData.selected_files = selectedContents.value.map(file => file.path)
}
```

### 2. 后端子任务拆分逻辑
```python
# 检测多文件
if selected_files and len(selected_files) > 1:
    # 创建主任务
    main_task_data = {
        "task_type": "main",
        "total_subtasks": len(selected_files),
        "selected_files": selected_files
    }
    
    # 创建子任务
    for i, video_file in enumerate(selected_files):
        subtask_data = {
            "task_type": "subtask", 
            "parent_task_id": main_task_id,
            "subtask_index": i + 1,
            "selected_files": [video_file],
            "video_file": video_file
        }
```

### 3. 任务管理界面显示
- **主任务**: 显示为"主任务"标签，包含子任务统计
- **子任务**: 显示为"子任务"标签，包含父任务关联
- **单任务**: 显示为"单任务"标签

## 🎉 验证方法

### 1. 创建多文件任务
1. 在发布管理页面选择2个或更多视频文件
2. 创建任务
3. 检查返回的任务类型是否为`main`

### 2. 检查数据库
```javascript
// 查询主任务
db.social_tasks.find({"task_type": "main"})

// 查询子任务
db.social_tasks.find({"task_type": "subtask", "parent_task_id": "主任务ID"})
```

### 3. 任务管理界面验证
1. 打开任务管理页面
2. 查看任务列表中的任务类型标签
3. 确认主任务显示子任务统计信息

## 🚀 后续优化

### 1. 进度聚合
- 主任务进度 = 所有子任务进度的平均值
- 主任务状态根据子任务状态自动更新

### 2. 批量操作
- 支持批量启动/暂停/取消子任务
- 主任务操作自动应用到所有子任务

### 3. 错误处理
- 子任务失败时主任务状态处理
- 部分子任务成功的情况处理

现在多视频任务可以正确拆分为子任务了！🎉
