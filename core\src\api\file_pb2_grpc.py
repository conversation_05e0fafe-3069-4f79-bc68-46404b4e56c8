# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from src.api import file_pb2 as file__pb2

GRPC_GENERATED_VERSION = '1.72.0rc1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in file_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class FileServiceStub(object):
    """文件服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetFilePaths = channel.unary_unary(
                '/file.FileService/GetFilePaths',
                request_serializer=file__pb2.FilePathsRequest.SerializeToString,
                response_deserializer=file__pb2.FilePathsResponse.FromString,
                _registered_method=True)
        self.ListDirectory = channel.unary_unary(
                '/file.FileService/ListDirectory',
                request_serializer=file__pb2.ListDirectoryRequest.SerializeToString,
                response_deserializer=file__pb2.ListDirectoryResponse.FromString,
                _registered_method=True)
        self.CheckPathExists = channel.unary_unary(
                '/file.FileService/CheckPathExists',
                request_serializer=file__pb2.PathExistsRequest.SerializeToString,
                response_deserializer=file__pb2.PathExistsResponse.FromString,
                _registered_method=True)


class FileServiceServicer(object):
    """文件服务
    """

    def GetFilePaths(self, request, context):
        """获取文件路径配置
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDirectory(self, request, context):
        """列出目录内容
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckPathExists(self, request, context):
        """检查路径是否存在
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FileServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetFilePaths': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFilePaths,
                    request_deserializer=file__pb2.FilePathsRequest.FromString,
                    response_serializer=file__pb2.FilePathsResponse.SerializeToString,
            ),
            'ListDirectory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDirectory,
                    request_deserializer=file__pb2.ListDirectoryRequest.FromString,
                    response_serializer=file__pb2.ListDirectoryResponse.SerializeToString,
            ),
            'CheckPathExists': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckPathExists,
                    request_deserializer=file__pb2.PathExistsRequest.FromString,
                    response_serializer=file__pb2.PathExistsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'file.FileService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('file.FileService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FileService(object):
    """文件服务
    """

    @staticmethod
    def GetFilePaths(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/GetFilePaths',
            file__pb2.FilePathsRequest.SerializeToString,
            file__pb2.FilePathsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDirectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/ListDirectory',
            file__pb2.ListDirectoryRequest.SerializeToString,
            file__pb2.ListDirectoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckPathExists(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/CheckPathExists',
            file__pb2.PathExistsRequest.SerializeToString,
            file__pb2.PathExistsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
