services:
  traefik:
    image: ${TRAEFIK_IMAGE}
    ports:
      - "8000:8000"  # API 网关入口
      - "8080:8080"  # Traefik 仪表板
    volumes:
      - ${DATA_STORAGE_DIR}/traefik:/etc/traefik
      - /var/run/docker.sock:/var/run/docker.sock:ro  # 添加 Docker socket 挂载
    depends_on:
      - consul
    networks:
      - thunderhub
    restart: unless-stopped

  frontend:
    image: ${CI_REGISTRY_IMAGE}-frontend:${IMAGE_TAG}
    ports:
      - "80:80"  # Nginx 直接服务
    environment:
      - ENV=development
    networks:
      - thunderhub
    restart: unless-stopped

  backend:
    image: ${CI_REGISTRY_IMAGE}-backend:${IMAGE_TAG}
    ports:
      - "8001:8000"  # 测试直接挂载
    environment:
      - ENV=development
      - DB_MONGODB_URL=mongodb://mongodb:27017
      - DB_REDIS_URL=redis://redis:6379/1
      - DB_CONSUL_URL=consul:8500
    depends_on:
      - mongodb
      - redis
      - consul
    labels:
       - "traefik.enable=true"
       - "traefik.http.services.backend-service.loadbalancer.server.port=8000"  # 仅定义服务端口
    networks:
      - thunderhub
    restart: unless-stopped

  mongodb:
    image: ${MONGO_IMAGE}
    ports:
      - "27017:27017"
    volumes:
      - ${DATA_STORAGE_DIR}/mongodb:/data/db
    networks:
      - thunderhub
    restart: unless-stopped

  redis:
    image: ${REDIS_IMAGE}
    ports:
      - "6379:6379"
    volumes:
      - ${DATA_STORAGE_DIR}/redis:/data
    networks:
      - thunderhub
    restart: unless-stopped

  consul:
    image: ${CONSUL_IMAGE}
    ports:
      - "8500:8500"  # Core 注册、开发环境访问
    volumes:
      - ${DATA_STORAGE_DIR}/consul:/consul/data
    command: ["agent", "-server", "-bootstrap", "-ui", "-client=0.0.0.0"]
    networks:
      - thunderhub
    restart: unless-stopped

networks:
  thunderhub:
    driver: bridge
