#!/usr/bin/env python3
"""
测试任务状态同步功能
用于验证Redis到MongoDB的任务状态同步是否正常工作
"""

import asyncio
import json
import logging
import redis.asyncio as redis
from pymongo import MongoClient
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.database import DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_specific_task_sync():
    """测试特定任务的状态同步"""
    try:
        # 直接使用配置（避免环境变量问题）
        redis_url = "redis://localhost:6379"
        mongo_url = "mongodb://localhost:27017"
        mongo_db_name = "social_media_automation"

        # 连接Redis
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")

        # 连接MongoDB
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client[mongo_db_name]
        logger.info("MongoDB连接成功")

        # 检查特定任务
        task_id = "ded8ab69-4f04-440f-856f-d645f31b64d7"
        logger.info(f"检查任务: {task_id}")

        # 从MongoDB获取任务状态
        mongo_task = mongo_db.social_tasks.find_one({"task_id": task_id})
        if mongo_task:
            logger.info(f"MongoDB中的任务状态:")
            logger.info(f"  状态: {mongo_task.get('status')}")
            logger.info(f"  进度: {mongo_task.get('progress', 0)}%")
            logger.info(f"  更新时间: {mongo_task.get('updated_at')}")
            logger.info(f"  开始时间: {mongo_task.get('start_time')}")
            logger.info(f"  结束时间: {mongo_task.get('end_time')}")
        else:
            logger.error(f"MongoDB中未找到任务: {task_id}")
            return

        # 从Redis获取任务状态
        redis_key = f"task:{task_id}:latest"
        redis_status = await redis_client.get(redis_key)

        if redis_status:
            try:
                redis_data = json.loads(redis_status)
                logger.info(f"Redis中的任务状态:")
                logger.info(f"  状态: {redis_data.get('status')}")
                logger.info(f"  进度: {redis_data.get('progress', 0)}%")
                logger.info(f"  结束时间: {redis_data.get('end_time')}")

                # 比较状态差异
                mongo_status = mongo_task.get('status')
                redis_status_val = redis_data.get('status')

                if mongo_status != redis_status_val:
                    logger.warning(f"❌ 状态不同步！MongoDB: {mongo_status}, Redis: {redis_status_val}")

                    # 手动同步状态
                    logger.info("尝试手动同步状态到MongoDB...")
                    update_data = {
                        "status": redis_data.get("status", "unknown"),
                        "progress": redis_data.get("progress", 0),
                        "updated_at": datetime.now()
                    }

                    if redis_data.get("status") in ["completed", "failed", "canceled"]:
                        if "end_time" in redis_data:
                            update_data["end_time"] = redis_data["end_time"]
                        else:
                            update_data["end_time"] = datetime.now().isoformat()

                    result = mongo_db.social_tasks.update_one(
                        {"task_id": task_id},
                        {"$set": update_data}
                    )

                    if result.modified_count > 0:
                        logger.info(f"✅ 手动同步成功！已更新任务状态为: {redis_data.get('status')}")
                    else:
                        logger.error("❌ 手动同步失败")
                else:
                    logger.info("✅ 状态已同步")

            except json.JSONDecodeError as e:
                logger.error(f"解析Redis数据失败: {str(e)}")
        else:
            logger.warning(f"Redis中未找到任务状态: {redis_key}")

            # 检查是否有其他相关的Redis键
            pattern_keys = await redis_client.keys(f"task:{task_id}:*")
            if pattern_keys:
                logger.info(f"找到相关的Redis键: {pattern_keys}")
                for key in pattern_keys:
                    value = await redis_client.get(key)
                    logger.info(f"  {key.decode()}: {value}")
            else:
                logger.warning(f"Redis中没有找到任何与任务{task_id}相关的键")

        # 关闭连接
        await redis_client.close()
        mongo_client.close()
        logger.info("检查完成")

    except Exception as e:
        logger.error(f"检查失败: {str(e)}", exc_info=True)


async def test_task_sync():
    """测试任务状态同步"""
    try:
        # 获取数据库配置
        db_config = DatabaseConfig()

        # 连接Redis
        redis_client = redis.from_url(db_config.redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")

        # 连接MongoDB
        mongo_client = MongoClient(db_config.mongo_url)
        mongo_db = mongo_client[db_config.mongo_db_name]
        logger.info("MongoDB连接成功")

        # 查找一个运行中的任务
        running_task = mongo_db.social_tasks.find_one({
            "status": {"$in": ["running", "pending"]},
            "task_id": {"$exists": True}
        })
        
        if not running_task:
            logger.warning("没有找到运行中的任务，创建一个测试任务")
            # 创建一个测试任务
            test_task_id = "test-task-" + datetime.now().strftime("%Y%m%d-%H%M%S")
            test_task = {
                "task_id": test_task_id,
                "status": "running",
                "progress": 0,
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "platform_id": "test",
                "account_id": "test"
            }
            mongo_db.social_tasks.insert_one(test_task)
            logger.info(f"创建测试任务: {test_task_id}")
            task_id = test_task_id
        else:
            task_id = running_task["task_id"]
            logger.info(f"找到运行中的任务: {task_id}")
        
        # 模拟Core服务发布任务状态更新
        test_status_data = {
            "task_id": task_id,
            "status": "failed",  # 模拟任务失败
            "progress": 50,
            "end_time": datetime.now().isoformat(),
            "logs": [
                {
                    "message": "测试任务状态同步",
                    "level": "info",
                    "timestamp": datetime.now().isoformat()
                },
                {
                    "message": "任务执行失败",
                    "level": "error", 
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
        
        # 发布到Redis
        channel = f"task:{task_id}:status"
        status_json = json.dumps(test_status_data)
        
        logger.info(f"发布任务状态更新到Redis频道: {channel}")
        logger.info(f"状态数据: {test_status_data}")
        
        # 发布消息
        result = await redis_client.publish(channel, status_json)
        logger.info(f"发布结果: {result}个客户端接收到消息")
        
        # 同时保存到Redis的latest键
        latest_key = f"task:{task_id}:latest"
        await redis_client.set(latest_key, status_json)
        await redis_client.expire(latest_key, 86400)  # 24小时过期
        logger.info(f"已保存最新状态到Redis键: {latest_key}")
        
        # 等待一段时间让Backend处理消息
        logger.info("等待5秒让Backend处理消息...")
        await asyncio.sleep(5)
        
        # 检查MongoDB中的任务状态是否已更新
        updated_task = mongo_db.social_tasks.find_one({"task_id": task_id})
        if updated_task:
            logger.info(f"MongoDB中的任务状态:")
            logger.info(f"  状态: {updated_task.get('status')}")
            logger.info(f"  进度: {updated_task.get('progress')}%")
            logger.info(f"  更新时间: {updated_task.get('updated_at')}")
            logger.info(f"  结束时间: {updated_task.get('end_time')}")
            
            if updated_task.get('status') == 'failed':
                logger.info("✅ 任务状态同步成功！")
            else:
                logger.warning(f"❌ 任务状态同步失败，期望: failed，实际: {updated_task.get('status')}")
        else:
            logger.error("❌ 无法找到更新后的任务")
        
        # 检查任务日志是否已插入
        logs = list(mongo_db.social_task_logs.find({"task_id": task_id}).sort("created_at", -1).limit(5))
        if logs:
            logger.info(f"找到 {len(logs)} 条任务日志:")
            for log in logs:
                logger.info(f"  [{log.get('level')}] {log.get('message')} ({log.get('timestamp')})")
        else:
            logger.warning("没有找到任务日志")
        
        # 清理测试数据
        if running_task is None:  # 如果是我们创建的测试任务
            mongo_db.social_tasks.delete_one({"task_id": task_id})
            mongo_db.social_task_logs.delete_many({"task_id": task_id})
            logger.info(f"已清理测试任务: {task_id}")
        
        # 关闭连接
        await redis_client.close()
        mongo_client.close()
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)


async def check_redis_sync_service():
    """检查Redis同步服务状态"""
    try:
        # 获取数据库配置
        db_config = DatabaseConfig()
        
        # 连接Redis
        redis_client = redis.from_url(db_config.redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")
        
        # 检查是否有订阅者
        channels = ["task:*:status", "device:all:changes"]
        for pattern in channels:
            try:
                # 发布一个测试消息
                test_data = {"test": True, "timestamp": datetime.now().isoformat()}
                result = await redis_client.publish("test:channel", json.dumps(test_data))
                logger.info(f"测试频道发布结果: {result}个订阅者")
            except Exception as e:
                logger.error(f"测试频道发布失败: {str(e)}")
        
        # 检查Redis中的任务状态键
        task_keys = await redis_client.keys("task:*:latest")
        logger.info(f"Redis中有 {len(task_keys)} 个任务状态键")
        
        if task_keys:
            # 显示前5个任务状态
            for i, key in enumerate(task_keys[:5]):
                try:
                    status_json = await redis_client.get(key)
                    if status_json:
                        status_data = json.loads(status_json)
                        logger.info(f"任务状态 {i+1}: {key.decode()} -> {status_data.get('status')} ({status_data.get('progress', 0)}%)")
                except Exception as e:
                    logger.error(f"读取任务状态失败: {str(e)}")
        
        await redis_client.close()
        
    except Exception as e:
        logger.error(f"检查Redis同步服务失败: {str(e)}", exc_info=True)


async def main():
    """主函数"""
    logger.info("开始测试任务状态同步功能")

    # 检查特定任务状态同步
    logger.info("=== 检查特定任务状态同步 ===")
    await test_specific_task_sync()

    # 检查Redis同步服务状态
    logger.info("=== 检查Redis同步服务状态 ===")
    await check_redis_sync_service()

    # 测试任务状态同步
    logger.info("=== 测试任务状态同步 ===")
    await test_task_sync()


if __name__ == "__main__":
    asyncio.run(main())
