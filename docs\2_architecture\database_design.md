# ThunderHub 数据库设计文档

**版本**: v1.3.0
**最后更新**: 2023/10/15

---

## 1. 技术选型

### 主数据库
- **MongoDB 5.0+**
  - 文档型数据库适合设备配置存储
  - 灵活的模式适应快速迭代
  - 高性能读写能力

### 缓存层
- **Redis 7.0**
  - 存储实时状态数据
  - 支持发布/订阅模式
  - 高并发访问支持

---

## 2. 数据流向

```mermaid
graph LR
    A[设备控制层] -->|实时状态| B[业务层状态缓存]
    B -->|定时快照| C[(MongoDB)]
    B -->|WebSocket推送| D[前端展示]
    C -->|历史查询| D
```

---

## 3. 分层存储策略

| 层级 | 存储内容 | 技术实现 | 保留时间 |
|------|----------|----------|----------|
| 热数据 | 实时状态信息 | Redis内存 | 会话期间 |
| 温数据 | 近期配置变更 | MongoDB内存映射 | 7天 |
| 冷数据 | 历史记录 | MongoDB磁盘存储 | 永久 |

---

## 4. 数据关系图

```mermaid
graph TD
    A[设备 devices] -->|一对多| M[设备账号映射 device_account_mappings]
    B[社交账号 social_accounts] -->|一对多| M
    C[平台 social_platforms] -->|一对多| B
    C -->|一对多| D[平台应用 platform_apps]
    D -->|一对多| M
    E[Core服务] -->|一对多| A
    E -->|一对多| B

    %% 约束关系
    subgraph 约束
        M -->|device_id+platform_id唯一| M
        M -->|device_id+account_id唯一| M
        B -->|username+platform_id唯一| B
    end
```

---

## 5. MongoDB数据结构设计

### 5.1 设备相关集合

#### devices 集合 (设备基础信息)

```javascript
{
  "_id": String,    // 设备唯一ID，通常是设备索引号
  "name": String,   // 设备名称，从雷电模拟器获取的真实名称
  "type": String,   // 设备类型，例如："雷电模拟器"
  "status": String, // 设备状态：running/stopped/starting/stopping/error/unknown
  "core_id": String,// Core服务ID，用于区分不同Core服务管理的设备
  "config": {       // 设备配置
    "resolution": String,  // 分辨率，例如："1080x1920"
    "cpu_cores": Number,   // CPU核心数
    "memory": Number,      // 内存大小(MB)
    "adb_port": Number     // ADB端口
  },
  "window_info": {  // 窗口信息
    "top_window": String,  // 顶层窗口句柄
    "bound_window": String // 绑定窗口句柄
  },
  "process_info": { // 进程信息
    "pid": String,         // 进程ID
    "vbox_pid": String     // VirtualBox进程ID
  },
  "cpu_usage": String,     // CPU使用率，例如："5.2%"
  "memory_usage": String,  // 内存使用率，例如："30.5%"
  "network_status": String,// 网络状态，例如："已连接"/"未连接"/"飞行模式"
  "created_at": Date,      // 创建时间
  "updated_at": Date,      // 更新时间
  "last_change": {         // 最后一次状态变更
    "old_status": String,  // 旧状态
    "new_status": String,  // 新状态
    "timestamp": Number    // 变更时间戳
  },
  "tags": [String]         // 设备标签（可选）
}
```

#### device_status_history 集合 (设备状态历史)

```javascript
{
  "_id": ObjectId,
  "device_id": String,    // 关联设备ID
  "timestamp": Date,      // 记录时间
  "status_data": {        // 完整状态快照
    "status": String,     // 设备状态
    "cpu_usage": String,  // CPU使用率
    "memory_usage": String, // 内存使用率
    "network_status": String, // 网络状态
    "window_info": {      // 窗口信息
      "top_window": String,
      "bound_window": String
    },
    "process_info": {     // 进程信息
      "pid": String,
      "vbox_pid": String
    }
  }
}
```

### 5.2 社交媒体相关集合

#### social_accounts 集合 (社交媒体账号)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 账号唯一标识符，用于API引用
  "username": String,    // 账号用户名，如"thunderhub_official"
  "password": String,    // 加密存储的密码
  "display_name": String,// 显示名称，如"雷电中心官方"
  "platform_id": String, // 关联的平台ID，如"youtube", "tiktok", "douyin"
  "status": String,      // 账号状态："active", "inactive", "suspended"
  "avatar": String,      // 头像URL，账号头像图片地址
  "description": String, // 账号描述，账号简介
  "followers": Number,   // 粉丝数量，定期更新
  "following": Number,   // 关注数量，定期更新
  "posts_count": Number, // 发布内容数量，定期更新
  "last_login": Date,    // 最后登录时间，记录账号最后一次登录时间
  "created_at": Date,    // 创建时间，账号添加到系统的时间
  "updated_at": Date,    // 更新时间，账号信息最后更新时间
  "auth_data": {         // 认证数据，包含token、cookie等认证信息，应加密存储
    "token": String,     // 加密的访问令牌
    "cookies": String,   // 加密的cookie数据
    "expires_at": Date   // 令牌过期时间
  },
  "settings": {          // 账号设置，账号特定的设置项
    "auto_reply": Boolean,   // 是否启用自动回复
    "notification": Boolean, // 是否启用通知
    "privacy_level": String  // 隐私级别："public", "private", "friends"
  },
  "tags": [String],      // 标签，用于分类和筛选账号
  "core_service_id": String // Core服务ID，用于区分不同Core服务管理的账号
}
```

#### social_platforms 集合 (社交媒体平台)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 平台唯一标识符，如"youtube", "tiktok"
  "name": String,        // 平台名称，如"YouTube", "TikTok"
  "icon": String,        // 平台图标文件名
  "website": String,     // 平台官方网站，如"https://www.youtube.com"
  "status": String,      // 平台状态："active", "inactive", "maintenance"
  "features": [String],  // 平台支持的功能，如["video", "live", "comment"]
  "created_at": Date,    // 创建时间
  "updated_at": Date     // 更新时间
}
```

#### platform_apps 集合 (平台应用)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "id": String,          // 应用唯一标识符
  "platform_id": String, // 关联的平台ID
  "name": String,        // 应用名称，如"YouTube Android App"
  "type": String,        // 应用类型："android", "ios", "web", "desktop"
  "status": String,      // 应用状态："active", "inactive", "deprecated"
  "version": String,     // 应用版本，如"18.20.38"
  "app_info": {          // 应用相关信息
    "package_name": String,     // 应用包名，如"com.google.android.youtube"
    "main_activity": String,    // 主Activity，如"com.google.android.youtube.HomeActivity"
    "min_android_version": String, // 最低Android版本要求，如"8.0"
    "download_url": String,     // 应用下载地址
  },
  "automation": {        // 自动化相关信息
    "type": String,      // 自动化类型："appium", "adb", "selenium", "hybrid"
    "selectors": {       // 常用选择器
      "login_button": String,   // 登录按钮选择器
      "post_button": String,    // 发布按钮选择器
      "comment_button": String, // 评论按钮选择器
      "like_button": String,    // 点赞按钮选择器
      "share_button": String    // 分享按钮选择器
    },
    "actions": {         // 常用操作流程
      "login": [         // 登录操作步骤
        {
          "action": String,     // 操作类型："click", "input", "swipe", "wait"
          "selector": String,   // 元素选择器
          "value": String       // 操作值（如输入文本）
        }
      ],
      "post": [          // 发布操作步骤
        {
          "action": String,
          "selector": String,
          "value": String
        }
      ]
    },
    "commands": {        // 常用命令
      "start_app": String,      // 启动应用命令
      "stop_app": String,       // 停止应用命令
      "clear_data": String      // 清除数据命令
    }
  },
  "created_at": Date,    // 创建时间
  "updated_at": Date     // 更新时间
}
```

#### device_account_mappings 集合 (设备账号关联)

```javascript
{
  "_id": ObjectId,       // MongoDB自动生成的ID
  "device_id": String,   // 设备ID，关联到devices表的id字段
  "account_id": String,  // 账号ID，关联到social_accounts表的id字段
  "platform_id": String, // 平台ID，关联到social_platforms表的id字段
  "app_id": String,      // 应用ID，关联到platform_apps表的id字段
  "status": String,      // 关联状态："active", "inactive"
  "created_at": Date,    // 创建时间
  "updated_at": Date,    // 更新时间
  "last_used": Date,     // 最后使用时间
  "core_service_id": String, // Core服务ID
  "settings": {          // 设备特定的账号设置
    "auto_login": Boolean,   // 是否自动登录
    "keep_alive": Boolean,   // 是否保持登录状态
    "notification": Boolean  // 是否启用通知
  }
}
```

## 6. Redis数据结构设计

### 6.1 Core服务管理

#### Core服务列表 (Set)
Key: `cores:all`
```
成员: 所有Core服务ID列表
```

#### Core服务设备集合 (Set)
Key: `core:{core_id}:devices`
```
成员: 特定Core服务管理的设备ID列表
```

### 6.2 设备状态管理

#### 设备完整状态 (String)
Key: `device:{core_id}:{device_id}:state`

```json
{
  "device_id": "0",
  "name": "雷电模拟器",
  "status": "running",
  "device_type": "雷电模拟器",
  "core_id": "server1",
  "window_info": {
    "top_window": "12345678",
    "bound_window": "87654321"
  },
  "process_info": {
    "pid": "1234",
    "vbox_pid": "5678"
  },
  "hardware_info": {
    "cpu_cores": 2,
    "memory_size": 2048,
    "cpu_usage": "5.2%",
    "memory_usage": "30.5%",
    "network_status": "已连接"
  },
  "last_sync": 1684123456
}
```

#### 设备硬件信息 (Hash)
Key: `device:{core_id}:{device_id}:hardware`

```
field          value
-------        ------
cpu_cores      2
memory_size    2048
cpu_usage      5.2%
memory_usage   30.5%
network_status 已连接
```

#### 设备分类集合 (Set)
Key: `devices:all`
```
成员: 所有设备ID列表
```

Key: `devices:status:{status}`
```
成员: 特定状态的设备ID列表
```

Key: `devices:type:{type}`
```
成员: 特定类型的设备ID列表
```

Key: `core:{core_id}:devices:status:{status}`
```
成员: 特定Core服务下特定状态的设备ID列表
```

### 6.3 设备监控数据

#### CPU使用率 (Hash)
Key: `devices:cpu_usage`
```
field(设备ID)  value(CPU使用率)
-------        ------
0              5.2%
1              3.8%
```

#### 内存使用率 (Hash)
Key: `devices:memory_usage`
```
field(设备ID)  value(内存使用率)
-------        ------
0              30.5%
1              25.2%
```

### 6.4 设备状态变更记录

#### 全局变更记录 (List)
Key: `device:changes`
```json
[
  {
    "device_id": "0",
    "core_id": "server1",
    "name": "雷电模拟器",
    "old_status": "stopped",
    "new_status": "running",
    "timestamp": 1684123456
  }
]
```

#### Core服务变更记录 (List)
Key: `core:{core_id}:changes`
```json
[
  {
    "device_id": "0",
    "name": "雷电模拟器",
    "old_status": "stopped",
    "new_status": "running",
    "timestamp": 1684123456
  }
]
```

#### 设备最后变更 (String)
Key: `device:{core_id}:{device_id}:last_change`
```json
{
  "old_status": "stopped",
  "new_status": "running",
  "timestamp": 1684123456
}
```

## 7. 索引策略

### 7.1 MongoDB索引

#### 设备集合索引
- `_id`: 主键索引
- `name`: 普通索引
- `type`: 普通索引
- `status`: 普通索引
- `core_id`: 普通索引，用于按Core服务筛选设备
- `type+status`: 复合索引，用于按类型和状态筛选设备
- `core_id+status`: 复合索引，用于按Core服务和状态筛选设备
- `core_id+type`: 复合索引，用于按Core服务和类型筛选设备
- `core_id+type+status`: 复合索引，用于按Core服务、类型和状态筛选设备
- `updated_at`: 普通索引，用于按更新时间排序

#### 历史记录索引
- `device_id`: 普通索引，用于查询特定设备的历史记录
- `core_id`: 普通索引，用于查询特定Core服务的历史记录
- `timestamp`: 普通索引，用于按时间查询
- `device_id+timestamp`: 复合索引，用于按设备和时间范围查询
- `core_id+timestamp`: 复合索引，用于按Core服务和时间范围查询
- `core_id+device_id+timestamp`: 复合索引，用于按Core服务、设备和时间范围查询

#### 社交账号索引
- `_id`: 主键索引
- `id`: 唯一索引，确保账号ID唯一
- `username+platform_id`: 唯一索引，确保同一平台下用户名唯一
- `platform_id`: 普通索引，用于按平台筛选账号
- `core_service_id`: 普通索引，用于按Core服务筛选账号
- `status`: 普通索引，用于筛选活跃账号
- `platform_id+status`: 复合索引，用于按平台和状态筛选账号
- `core_service_id+platform_id`: 复合索引，用于按Core服务和平台筛选账号
- `created_at`: 普通索引，用于按创建时间排序
- `updated_at`: 普通索引，用于按更新时间排序

#### 社交平台索引
- `_id`: 主键索引
- `id`: 唯一索引，确保平台ID唯一
- `status`: 普通索引，用于筛选活跃平台
- `created_at`: 普通索引，用于按创建时间排序

#### 平台应用索引
- `_id`: 主键索引
- `id`: 唯一索引，确保应用ID唯一
- `platform_id`: 普通索引，用于按平台筛选应用
- `type`: 普通索引，用于按应用类型筛选
- `status`: 普通索引，用于筛选活跃应用
- `platform_id+type`: 复合索引，用于按平台和类型筛选应用

#### 设备账号关联索引
- `_id`: 主键索引
- `device_id`: 普通索引，用于按设备查询关联的账号
- `account_id`: 普通索引，用于按账号查询关联的设备
- `platform_id`: 普通索引，用于按平台查询关联
- `app_id`: 普通索引，用于按应用查询关联
- `core_service_id`: 普通索引，用于按Core服务查询关联
- `status`: 普通索引，用于筛选活跃关联
- `device_id+platform_id`: 唯一索引，确保一个设备对于每个平台只能关联一个账号
- `device_id+account_id`: 唯一索引，确保一个设备只能关联一个特定账号一次

## 8. 数据同步流程

```mermaid
sequenceDiagram
    participant Core1 as Core模块1
    participant Core2 as Core模块2
    participant Redis as Redis缓存
    participant Backend as 后端服务
    participant MongoDB as MongoDB
    participant Frontend as 前端

    Core1->>Redis: 注册Core服务(cores:all)
    Core2->>Redis: 注册Core服务(cores:all)

    Core1->>Core1: 采集设备信息
    Core2->>Core2: 采集设备信息

    Core1->>Redis: 推送设备状态(device:{core_id}:{device_id}:state)
    Core2->>Redis: 推送设备状态(device:{core_id}:{device_id}:state)

    Core1->>Redis: 更新设备列表(core:{core_id}:devices)
    Core2->>Redis: 更新设备列表(core:{core_id}:devices)

    Redis-->>Backend: 发布状态变更通知
    Backend->>Frontend: WebSocket推送实时状态(包含core_id)

    loop 定时快照
        Backend->>Redis: 获取所有Core服务ID
        Backend->>Redis: 获取每个Core服务的设备列表
        Backend->>Redis: 获取所有设备状态
        Backend->>MongoDB: 保存设备状态快照(包含core_id)
    end

    Frontend->>Backend: 请求设备列表(可选core_id参数)
    Backend->>MongoDB: 查询设备信息(可按core_id筛选)
    MongoDB-->>Backend: 返回设备数据
    Backend-->>Frontend: 返回设备列表
```