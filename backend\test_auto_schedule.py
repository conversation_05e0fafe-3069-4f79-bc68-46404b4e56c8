#!/usr/bin/env python3
"""
测试自动调度机制
手动触发第二个子任务的启动
"""

import asyncio
import logging
from pymongo import MongoClient
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_auto_schedule():
    """测试自动调度机制"""
    try:
        # 连接MongoDB
        mongo_client = MongoClient("mongodb://192.168.123.137:27017")
        mongo_db = mongo_client.thunderhub
        
        # 主任务ID
        main_task_id = "30a59348-b598-44ab-af57-98ea55347206"
        
        # 查找所有子任务
        all_subtasks = list(mongo_db.social_tasks.find({
            "parent_task_id": main_task_id,
            "task_type": "subtask"
        }).sort("subtask_index", 1))
        
        logger.info(f"找到{len(all_subtasks)}个子任务")
        
        # 统计子任务状态
        completed_count = 0
        failed_count = 0
        next_pending_task = None
        
        for subtask in all_subtasks:
            status = subtask.get("status")
            task_id = subtask.get("task_id")
            index = subtask.get("subtask_index")
            
            logger.info(f"子任务{index}: {task_id} - 状态: {status}")
            
            if status == "completed":
                completed_count += 1
            elif status == "failed":
                failed_count += 1
            elif status == "pending" and next_pending_task is None:
                next_pending_task = subtask
                
        total_subtasks = len(all_subtasks)
        logger.info(f"子任务状态统计: 总数{total_subtasks}, 完成{completed_count}, 失败{failed_count}")
        
        if next_pending_task:
            next_task_id = next_pending_task["task_id"]
            logger.info(f"找到待启动的子任务: {next_task_id}")
            
            # 手动启动下一个子任务
            await start_next_subtask(mongo_db, next_task_id)
        else:
            logger.info("没有找到待启动的子任务")
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
    finally:
        if 'mongo_client' in locals():
            mongo_client.close()


async def start_next_subtask(mongo_db, task_id: str):
    """启动下一个子任务"""
    try:
        from app.core.schemas.social_repository import SocialDatabaseService
        from app.core.client import TaskServiceClient
        
        logger.info(f"开始启动子任务: {task_id}")
        
        # 获取子任务信息
        subtask = mongo_db.social_tasks.find_one({"task_id": task_id})
        if not subtask:
            logger.error(f"找不到子任务: {task_id}")
            return
            
        # 分配设备ID（从第一个子任务获取）
        parent_task_id = subtask.get("parent_task_id")
        sibling_task = mongo_db.social_tasks.find_one({
            "parent_task_id": parent_task_id,
            "task_type": "subtask",
            "device_id": {"$exists": True, "$ne": None}
        })
        
        device_id = "85"  # 默认设备
        if sibling_task:
            device_id = sibling_task.get("device_id", "85")
            
        logger.info(f"为子任务{task_id}分配设备: {device_id}")
        
        # 更新子任务状态
        update_data = {
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "updated_at": datetime.now(),
            "device_id": device_id
        }
        
        result = mongo_db.social_tasks.update_one(
            {"task_id": task_id},
            {"$set": update_data}
        )
        
        logger.info(f"更新子任务状态结果: matched={result.matched_count}, modified={result.modified_count}")
        
        # 重新获取更新后的任务
        subtask = mongo_db.social_tasks.find_one({"task_id": task_id})
        
        # 使用Core服务客户端
        core_client = TaskServiceClient()
        
        # 创建任务
        logger.info(f"在Core服务中创建任务: {task_id}")
        create_result = await core_client.create_task(subtask)
        logger.info(f"创建任务结果: {create_result}")
        
        if not create_result.get("success"):
            logger.error(f"创建任务失败: {create_result.get('error')}")
            return
            
        # 启动任务
        logger.info(f"在Core服务中启动任务: {task_id}")
        start_result = await core_client.start_task(task_id)
        logger.info(f"启动任务结果: {start_result}")
        
        if start_result.get("success"):
            logger.info(f"✅ 子任务{task_id}启动成功")
        else:
            logger.error(f"❌ 子任务{task_id}启动失败: {start_result.get('error')}")
            
    except Exception as e:
        logger.error(f"启动子任务{task_id}失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(test_auto_schedule())
