# core package initialization
import logging
import os
from pathlib import Path

# 配置core模块独立日志
log_file = Path(__file__).parent.parent / 'logs' / 'core.log'
log_file.parent.mkdir(parents=True, exist_ok=True)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 设置core模块日志级别
logger = logging.getLogger('core')
logger.setLevel(logging.DEBUG)
logger.propagate = True
logger.info("Core模块日志系统初始化完成")