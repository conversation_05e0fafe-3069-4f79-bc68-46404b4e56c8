# YouTube短视频上传工作流配置
workflow:
  name: "YouTube短视频上传"
  description: "完整的YouTube短视频上传流程"
  version: "1.0"

  # 工作流步骤
  steps:
    - name: "点击创建按钮"
      id: "click_create"
      description: "点击YouTube主界面的创建按钮（+按钮）"
      action: "click"
      element: "create_button"
      wait_after: 2
      required: true

    - name: "处理草稿对话框"
      id: "handle_draft_dialog"
      description: "如果有草稿对话框，选择重新开始"
      action: "click_if_exists"
      element: "draft_restart_button"
      wait_after: 2
      required: false
      notes: "可选步骤，只有存在草稿时才会出现对话框，在选择短视频之前处理"

    - name: "选择短视频选项"
      id: "select_shorts"
      description: "在创建选项中选择短视频"
      action: "click"
      element: "shorts_option"
      wait_after: 3
      required: true

    - name: "点击导入照片库"
      id: "click_gallery"
      description: "点击导入照片库中的视频按钮"
      action: "click"
      element: "gallery_button"
      wait_after: 2
      required: true

    - name: "选择视频文件"
      id: "select_video"
      description: "在相册中选择要上传的视频文件"
      action: "click"
      element: "video_file"
      wait_after: 2
      required: true
      parameters:
        - name: "filename"
          type: "string"
          description: "视频文件名"

    - name: "完成视频编辑"
      id: "finish_editing"
      description: "完成短视频的裁剪和编辑"
      action: "click"
      element: "finish_editing_button"
      wait_after: 3
      required: true
      notes: "点击完成按钮"

    - name: "等待视频处理"
      id: "wait_video_processing"
      description: "等待YouTube处理视频，直到添加音效按钮出现"
      action: "wait_for_element"
      element: "add_music_button"
      timeout: 300
      required: true
      notes: "通过检测添加音效按钮是否出现来判断视频处理是否完成，最长等待5分钟"

    - name: "点击添加音效按钮"
      id: "click_add_music"
      description: "点击添加音效按钮"
      action: "click"
      element: "add_music_button"
      wait_after: 3
      required: false
      condition: "selectedMusic.length > 0"
      notes: "只有选择了音乐才执行此步骤"

    - name: "点击已保存标签页"
      id: "click_saved_music_tab"
      description: "点击已保存音乐标签页"
      action: "click"
      element: "saved_music_tab"
      wait_after: 3
      required: false
      condition: "selectedMusic.length > 0"
      notes: "使用已保存音乐策略"

    - name: "选择音乐"
      id: "select_music"
      description: "从已保存音乐中选择指定音乐"
      action: "select_music_item"
      element: "saved_music_item"
      wait_after: 2
      required: false
      condition: "selectedMusic.length > 0"
      parameters:
        - name: "musicId"
          type: "string"
          description: "音乐ID"
      notes: "根据音乐ID选择对应的音乐"

    - name: "添加音乐到视频"
      id: "add_music_to_video"
      description: "点击添加音乐到视频按钮"
      action: "click"
      element: "add_music_to_video_button"
      wait_after: 3
      required: false
      condition: "selectedMusic.length > 0"
      notes: "确认添加选中的音乐"

    - name: "关闭音乐选择器"
      id: "close_music_picker"
      description: "如果没有找到音乐，关闭音乐选择器"
      action: "click"
      element: "music_picker_close_button"
      wait_after: 2
      required: false
      condition: "selectedMusic.length > 0"
      notes: "备用步骤，当音乐选择失败时使用"

    - name: "点击音量设置按钮"
      id: "click_volume_settings"
      description: "点击音量调节按钮"
      action: "click"
      element: "volume_settings_button"
      wait_after: 3
      required: false
      condition: "keepOriginalAudio == true"
      notes: "只有需要保留原声时才执行此步骤"

    - name: "调节背景音乐音量"
      id: "adjust_music_volume"
      description: "调节背景音乐音量"
      action: "adjust_slider"
      element: "music_volume_slider"
      wait_after: 1
      required: false
      condition: "keepOriginalAudio == true"
      parameters:
        - name: "percentage"
          type: "integer"
          description: "音量百分比"
          source: "musicVolumePercentage"
      notes: "根据前端设置调节背景音乐音量"

    - name: "调节原声音量"
      id: "adjust_original_volume"
      description: "调节原声音量"
      action: "adjust_slider"
      element: "original_audio_volume_slider"
      wait_after: 1
      required: false
      condition: "keepOriginalAudio == true"
      parameters:
        - name: "percentage"
          type: "integer"
          description: "音量百分比"
          source: "originalAudioPercentage"
      notes: "根据前端设置调节原声音量"

    - name: "完成音频设置"
      id: "finish_audio_settings"
      description: "点击音频设置完成按钮"
      action: "click"
      element: "audio_settings_done_button"
      wait_after: 2
      required: false
      condition: "keepOriginalAudio == true"
      notes: "完成音频设置并关闭设置界面"

    - name: "前往编辑器"
      id: "go_to_editor"
      description: "点击前往编辑器按钮进入下一步"
      action: "click"
      element: "go_to_editor_button"
      wait_after: 3
      required: true
      notes: "无论是否选择音乐，都必须点击此按钮才能进入视频编辑界面"

    - name: "编辑器下一步"
      id: "editor_next_step"
      description: "在编辑器界面点击下一步按钮"
      action: "click"
      element: "editor_next_button"
      wait_after: 2
      required: true
      notes: "进入编辑器后，点击下一步按钮进入标题输入界面"

    - name: "输入视频标题"
      id: "input_title"
      description: "输入视频标题"
      action: "input_text"
      element: "title_input"
      wait_after: 1
      required: true
      parameters:
        - name: "title"
          type: "string"

    - name: "输入视频描述"
      id: "input_description"
      description: "输入视频描述"
      action: "input_text"
      element: "description_input"
      wait_after: 1
      required: false
      parameters:
        - name: "description"
          type: "string"
          description: "视频描述"

    - name: "点击隐私设置按钮"
      id: "click_privacy_button"
      description: "点击隐私设置按钮"
      action: "click"
      element: "privacy_button"
      wait_after: 2
      required: true
      notes: "打开隐私选项界面"

    - name: "选择隐私选项"
      id: "select_privacy_option"
      description: "选择具体的隐私选项"
      action: "select_privacy"
      element: "privacy_option_dynamic"
      wait_after: 1
      required: true
      parameters:
        - name: "privacy"
          type: "string"
          description: "隐私设置"
          options: ["public", "unlisted", "private"]
          source: "privacy"
      notes: "根据前端设置选择公开、不公开或私享选项。默认为公开。"

    - name: "点击预定时间切换按钮"
      id: "click_schedule_toggle"
      description: "点击预定时间切换按钮"
      action: "click"
      element: "schedule_toggle_button"
      wait_after: 2
      required: false
      condition: "isScheduled == true"
      notes: "只有设置了预定发布时才执行此步骤"

    - name: "点击时间设置区域"
      id: "click_time_setting"
      description: "点击时间设置区域"
      action: "click"
      element: "publish_time_setting"
      wait_after: 2
      required: false
      condition: "isScheduled == true"
      notes: "打开时间选择器"

    - name: "设置发布时间"
      id: "set_publish_time_value"
      description: "设置具体的发布时间"
      action: "input_time"
      element: "time_picker"
      wait_after: 1
      required: false
      condition: "isScheduled == true"
      parameters:
        - name: "publishTime"
          type: "string"
          description: "发布时间"
          source: "publishTime"
      notes: "根据前端设置的时间进行设置"

    - name: "返回主页面"
      id: "back_to_main"
      description: "点击返回按钮回到主页面"
      action: "click"
      element: "back_to_main_button"
      wait_after: 2
      required: true
      notes: "设置完隐私和发布时间后，点击返回按钮回到主页面，准备完成上传。"

    - name: "完成上传"
      id: "final_upload"
      description: "点击上传短视频按钮完成上传"
      action: "click"
      element: "final_upload_button"
      wait_after: 3
      required: true
      notes: "点击最终的上传短视频按钮，完成整个上传流程。"

    - name: "等待上传处理"
      id: "wait_upload_processing"
      description: "等待YouTube处理上传"
      action: "wait"
      timeout: 30
      required: false
      notes: "等待YouTube处理上传，可选步骤。"



# 工作流配置
config:
  # 全局超时设置
  global_timeout: 1800  # 30分钟

  # 重试配置
  retry:
    max_retries: 3
    retry_delay: 2
    retry_on_failure: true

  # 错误处理
  error_handling:
    continue_on_optional_failure: true
    screenshot_on_error: true
    log_level: "INFO"

  # 进度报告
  progress:
    report_interval: 5  # 每5秒报告一次进度
    detailed_logging: true
