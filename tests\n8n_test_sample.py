import requests
import json
import uuid
from typing import Dict, Any, List

class N8NNodeCreator:
    """n8n节点创建工具"""
    
    def __init__(self, base_url: str, api_key: str):
        """
        初始化创建器
        :param base_url: n8n基础地址 如 http://localhost:5678
        :param api_key: n8n API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "X-N8N-API-KEY": api_key,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def create_workflow(self, name: str, active: bool = True, tags: List[str] = None) -> Dict[str, Any]:
        """
        创建新工作流(严格遵循n8n API规范)
        :param name: 工作流名称(必需)
        :param active: 是否立即激活工作流(默认True)
        :param tags: 工作流标签列表(可选)
        :return: 创建的工作流完整信息
        :raises: RequestException 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows"
        payload = {
            "name": name,
            "nodes": [],
            "connections": {},
            "settings": {
                "saveExecutionProgress": True,
                "saveManualExecutions": True,
                "timezone": "Asia/Shanghai"
            }
        }
        
        try:
            print(f"创建新工作流 - 请求URL: {url}")
            print(f"请求头: {json.dumps(self.headers, indent=2)}")
            print(f"请求体: {json.dumps(payload, indent=2)}")
            
            response = requests.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            print(f"响应状态码: {response.status_code}")
            if response.status_code != 201:
                print(f"响应内容: {response.text}")
            
            response.raise_for_status()
            result = response.json()
            print(f"工作流创建成功，ID: {result.get('id')}")
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"创建工作流失败: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"\n错误详情: {e.response.text}"
            print(error_msg)
            raise

    def create_node(self, workflow_id: str, node_type: str, parameters: Dict[str, Any],
                  name: str = None, position: List[int] = None,
                  extra_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建工作流节点(通过更新工作流实现)
        :param workflow_id: 工作流ID
        :param node_type: 节点类型
        :param parameters: 节点参数
        :param name: 节点名称(可选)
        :param position: 节点位置[x,y](可选)
        :param extra_fields: 额外节点字段(如webhookId等)
        :return: 更新后的工作流信息
        :raises: RequestException 当API请求失败时抛出
        """
        # 先获取当前工作流
        workflow = self.get_workflow(workflow_id)
        
        # 创建新节点
        new_node = {
            "type": node_type,
            "parameters": parameters,
            "name": name or f"{node_type}节点",
            "position": position or [100, 100]
        }
        
        # 添加额外字段
        if extra_fields:
            new_node.update(extra_fields)
        
        # 添加到工作流节点列表
        workflow["nodes"].append(new_node)
        
        # 更新工作流
        return self.update_workflow(
            workflow_id=workflow_id,
            new_name=workflow["name"],
            new_nodes=workflow["nodes"],
            connections=workflow.get("connections", {})
        )

    def create_http_node(self, workflow_id: str, url: str, method: str = "GET",
                       headers: Dict = None, body: Dict = None,
                       name: str = None, position: List[int] = None) -> Dict[str, Any]:
        """
        创建HTTP请求节点
        :param workflow_id: 工作流ID
        :param url: 请求URL
        :param method: HTTP方法(默认GET)
        :param headers: 请求头(可选)
        :param body: 请求体(可选)
        :param name: 节点名称(可选)
        :param position: 节点位置[x,y](可选)
        :return: 创建的节点信息
        :raises: RequestException 当API请求失败时抛出
        """
        parameters = {
            "url": url,
            "method": method,
            "headers": headers or {},
            "body": body  # 直接传递字典，不需要json.dumps
        }
        
        return self.create_node(
            workflow_id=workflow_id,
            node_type="n8n-nodes-base.httpRequest",
            parameters=parameters,
            name=name or f"HTTP {method}请求",
            position=position or [100, 100]
        )
        

    def get_workflows(self) -> Dict[str, Any]:
        """
        获取所有工作流列表
        :return: 工作流列表
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows"
        print(f"获取工作流列表: {url}")
        response = requests.get(url, headers=self.headers)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.json()

    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        获取单个工作流详情
        :param workflow_id: 工作流ID
        :return: 工作流详情
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        print(f"获取工作流详情: {url}")
        response = requests.get(url, headers=self.headers)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.json()

    def activate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        激活工作流
        :param workflow_id: 工作流ID
        :return: 激活结果
        :raises: RequestException 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/activate"
        try:
            response = requests.post(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_msg = f"激活工作流失败: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"\n错误详情: {e.response.text}"
            raise Exception(error_msg)

    def update_workflow(self, workflow_id: str, new_name: str, new_nodes: List[Dict] = None,
                      connections: Dict = None) -> Dict[str, Any]:
        """
        更新工作流(严格遵循n8n API规范)
        :param workflow_id: 工作流ID(必需)
        :param new_name: 新的工作流名称(直接字符串)
        :param new_nodes: 要添加的新节点列表(可选)
        :param connections: 连接关系(可选)
        :return: 更新结果
        :raises: RequestException 当API请求失败时抛出
        """
        # 先获取当前工作流
        current_workflow = self.get_workflow(workflow_id)
        
        # 合并节点(保留已有节点，添加新节点)
        nodes = current_workflow.get("nodes", [])
        if new_nodes:
            nodes.extend(new_nodes)
            
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        payload = {
            "name": new_name,
            "nodes": nodes,
            "connections": connections or current_workflow.get("connections", {}),
            "settings": {
                "saveExecutionProgress": True,
                "saveManualExecutions": True,
                "timezone": "Asia/Shanghai"
            }
        }
        
        try:
            print(f"更新工作流 - 请求URL: {url}")
            print(f"更新数据: {json.dumps(payload, indent=2)}")
            
            response = requests.put(
                url,
                headers=self.headers,
                json=payload
            )
            
            print(f"响应状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"响应内容: {response.text}")
            
            response.raise_for_status()
            result = response.json()
            print(f"工作流更新成功，ID: {result.get('id')}")
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"更新工作流失败: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"\n错误详情: {e.response.text}"
            print(error_msg)
            raise

    def delete_workflow(self, workflow_id: str) -> bool:
        """
        删除工作流
        :param workflow_id: 工作流ID
        :return: 是否删除成功
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        print(f"删除工作流: {url}")
        response = requests.delete(url, headers=self.headers)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.status_code == 200

    def get_nodes(self, workflow_id: str) -> list[Dict[str, Any]]:
        """
        获取工作流中的所有节点
        :param workflow_id: 工作流ID
        :return: 节点列表
        :raises: HTTPError 当API请求失败时抛出
        """
        workflow = self.get_workflow(workflow_id)
        return workflow.get("nodes", [])

    def get_node(self, workflow_id: str, node_id: str) -> Dict[str, Any]:
        """
        获取单个节点详情
        :param workflow_id: 工作流ID
        :param node_id: 节点ID
        :return: 节点详情
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/nodes/{node_id}"
        print(f"获取节点详情: {url}")
        response = requests.get(url, headers=self.headers)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.json()

    def update_node(self, workflow_id: str, node_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新节点
        :param workflow_id: 工作流ID
        :param node_id: 节点ID
        :param update_data: 更新数据
        :return: 更新结果
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/nodes/{node_id}"
        print(f"更新节点: {url}")
        print(f"更新数据: {update_data}")
        response = requests.patch(url, headers=self.headers, json=update_data)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.json()

    def delete_node(self, workflow_id: str, node_id: str) -> bool:
        """
        删除节点
        :param workflow_id: 工作流ID
        :param node_id: 节点ID
        :return: 是否删除成功
        :raises: HTTPError 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/nodes/{node_id}"
        print(f"删除节点: {url}")
        response = requests.delete(url, headers=self.headers)
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()
        return response.status_code == 200

    def add_webhook_trigger(self, workflow_id: str) -> Dict[str, Any]:
        """
        添加Webhook触发器节点
        :param workflow_id: 工作流ID
        :return: 创建的触发器节点信息(包含webhookId)
        :raises: RequestException 当API请求失败时抛出
        """
        # 生成唯一的webhookId和path
        webhook_id = str(uuid.uuid4())
        webhook_path = str(uuid.uuid4())
        
        webhook_node = {
            "type": "n8n-nodes-base.webhook",
            "parameters": {
                "httpMethod": "GET",
                "path": webhook_path,
                "responseMode": "onReceived",
                "options": {}
            },
            "name": "Webhook Trigger",
            "position": [100, 100],
            "webhookId": webhook_id  # 使用UUID生成唯一的webhookId
        }
        
        # 创建节点并返回完整信息
        extra_fields = {
            "webhookId": webhook_id
        }
        node_info = self.create_node(
            workflow_id,
            webhook_node["type"],
            webhook_node["parameters"],
            webhook_node["name"],
            webhook_node["position"],
            extra_fields=extra_fields
        )
        return node_info
    def get_executions(self, workflow_id: str, limit: int = 10, status: str = None,
                     include_data: bool = False) -> Dict[str, Any]:
        """
        获取工作流执行历史(严格遵循n8n API规范)
        :param workflow_id: 工作流ID(必需)
        :param limit: 返回结果数量限制(默认10)
        :param status: 过滤执行状态(waiting,running,success,failed,crashed)
        :param include_data: 是否包含执行数据(默认False)
        :return: 执行历史列表(包含分页信息)
        :raises: RequestException 当API请求失败时抛出
        """
        url = f"{self.base_url}/api/v1/executions"
        params = {
            "workflowId": workflow_id,
            "limit": limit,
            "status": status,
            "includeData": str(include_data).lower(),  # 新增包含数据参数
            # "order": "DESC"  # 移除不支持的参数
        }
        params = {k: v for k, v in params.items() if v is not None}
        
        try:
            print(f"获取执行历史 - 请求URL: {url}")
            print(f"查询参数: {json.dumps(params, indent=2)}")
            
            response = requests.get(
                url,
                headers=self.headers,
                params=params,
                timeout=30
            )
            
            print(f"响应状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"响应内容: {response.text}")
            
            response.raise_for_status()
            result = response.json()
            print(f"获取到 {len(result.get('data', []))} 条执行记录")
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"获取执行历史失败: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"\n错误详情: {e.response.text}"
            print(error_msg)
            raise

# 使用示例
if __name__ == "__main__":
    # 安全警告：实际项目中建议使用环境变量管理敏感信息
    API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJmMjhlYzZhYS1mNTllLTQ3M2UtOWEyMC05YmNjOTQ5M2M2MDUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzQzNTE2ODQ3fQ.qpnWu6RTPcUDWFYz4JgO9_m5WIDM1kOgIVUpKGtOd14"
    creator = N8NNodeCreator("http://localhost:5678", API_KEY)

    # 1. 创建工作流
    try:
        workflow = creator.create_workflow("自动化测试工作流")
        workflow_id = workflow["id"]
        print(f"工作流创建成功，ID: {workflow_id}")
    except Exception as e:
        print("创建工作流失败:", str(e))
        exit(1)

    # 2. 更新工作流添加Webhook节点
    try:
        # 添加Webhook触发器（只在这里添加一次）
        webhook_node = creator.add_webhook_trigger(workflow_id)
        print(f"Webhook触发器创建成功")

        
        # 激活工作流
        creator.activate_workflow(workflow_id)
        print("工作流已激活")

        
    except Exception as e:
        print("工作流配置或执行失败:", str(e))
        exit(1)

    # 2. 获取所有工作流
    try:
        workflows = creator.get_workflows()
        print("当前工作流列表:", workflows)
    except Exception as e:
        print("获取工作流列表失败:", str(e))

    # 3. 更新工作流名称
    try:
        updated = creator.update_workflow(workflow_id, "更新后的工作流名称")
        print("工作流更新成功:", updated)
    except Exception as e:
        print("更新工作流失败:", str(e))

    '''
    # 4. 创建MCP服务控制节点（已注释掉）
    mcp_nodes = [
        # Appium控制节点
        {
            "type": "n8n-nodes-base.httpRequest",
            "name": "启动Appium服务",
            "parameters": {
                "url": "http://localhost:8080/mcp",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "tool": "start_appium",
                    "args": {"port": 4723}
                }
            },
            "position": [200, 200]
        },
        # 模拟器控制节点
        {
            "type": "n8n-nodes-base.httpRequest",
            "name": "启动模拟器",
            "parameters": {
                "url": "http://localhost:8080/mcp",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "tool": "start_emulator",
                    "args": {"instance": 0}
                }
            },
            "position": [400, 200]
        },
        # 设备列表查询节点
        {
            "type": "n8n-nodes-base.httpRequest",
            "name": "获取设备列表",
            "parameters": {
                "url": "http://localhost:8080/mcp",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "tool": "list_devices",
                    "args": {}
                }
            },
            "position": [600, 200]
        }
    ]
    
    # 5. 创建连接节点
    connection_node = {
        "type": "n8n-nodes-base.function",
        "name": "结果处理器",
        "parameters": {
            "functionCode": "// 处理MCP服务返回结果\nconst result = $input.all()[0].json;\nif (result.error) {\n  throw new Error(result.error);\n}\nreturn result;"
        },
        "position": [800, 200]
    }
    '''
    
    try:
        '''
        # 创建所有MCP服务节点（已注释掉）
        node_ids = []
        for node in mcp_nodes:
            result = creator.create_http_node(workflow_id, node)
            node_ids.append(result["id"])
            print(f"节点 {node['name']} 创建成功, ID: {result['id']}")
        
        # 创建连接节点
        result = creator.create_http_node(workflow_id, connection_node)
        node_ids.append(result["id"])
        print(f"节点 {connection_node['name']} 创建成功, ID: {result['id']}")
        '''
        
      
        # 7. 触发Webhook测试
        # 获取工作流最新信息
        workflow = creator.get_workflow(workflow_id)
        print(f"\n完整工作流信息: {workflow}")
        
        # 从工作流节点中获取Webhook节点
        webhook_nodes = [n for n in workflow['nodes'] if n['type'] == 'n8n-nodes-base.webhook']
        if not webhook_nodes:
            raise Exception("工作流中没有找到Webhook节点")
            
        webhook_node = webhook_nodes[0]
        
        # 生成触发URL - 仅使用path参数
        if 'parameters' in webhook_node and 'path' in webhook_node['parameters']:
            trigger_url = f"http://localhost:5678/webhook/{webhook_node['parameters']['path']}"
        else:
            raise Exception("Webhook节点缺少path参数")
            
        print(f"生成的Webhook触发URL: {trigger_url}")
        print(f"\n触发Webhook测试: {trigger_url}")
        response = requests.get(trigger_url)
        print(f"触发响应状态码: {response.status_code}")
        
        # 8. 获取工作流中的所有节点
        nodes = creator.get_nodes(workflow_id)
        print("\n工作流节点列表:", nodes)
        
        # 9. 获取执行历史
        executions = creator.get_executions(workflow_id)
        print("\n最近执行记录:", executions)
        
        
    except Exception as e:
        print("操作失败:", str(e))