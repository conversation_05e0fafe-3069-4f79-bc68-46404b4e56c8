#!/usr/bin/env python3
"""
测试连接池配置
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.services.common.device_manager import DeviceManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_connection_pool():
    """测试连接池配置"""
    print("🔧 测试连接池配置...")
    
    # 创建设备管理器
    device_manager = DeviceManager("85")  # 使用你的设备ID
    
    print("✅ 设备管理器已创建，连接池已配置")
    print("📊 连接池配置应该已经生效，最大连接数: 10")
    
    # 测试连接（可选，如果Appium服务器正在运行）
    try:
        print("🔌 尝试连接到设备...")
        success = await device_manager.connect()
        if success:
            print("✅ 设备连接成功")
            await device_manager.disconnect()
            print("✅ 设备连接已断开")
        else:
            print("❌ 设备连接失败（这是正常的，如果Appium服务器未运行）")
    except Exception as e:
        print(f"⚠️ 连接测试异常: {str(e)}（这是正常的，如果Appium服务器未运行）")
    
    print("🎉 连接池配置测试完成")

if __name__ == "__main__":
    asyncio.run(test_connection_pool())
