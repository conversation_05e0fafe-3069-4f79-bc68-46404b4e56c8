{"name": "thunderhub-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.0", "date-fns": "^4.1.0", "echarts": "^5.4.3", "element-plus": "^2.3.0", "jschardet": "^3.1.4", "pinia": "^2.3.1", "socket.io-client": "^4.7.2", "vite": "^3.0.0", "vue": "^3.0.0", "vue-router": "^4.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.0", "typescript": "^4.0.0"}, "main": "vite.config.js", "author": "", "license": "ISC"}